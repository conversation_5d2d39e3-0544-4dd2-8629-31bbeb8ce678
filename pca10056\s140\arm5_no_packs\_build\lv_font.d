.\_build\lv_font.o: ..\lvgl\src\font\lv_font.c
.\_build\lv_font.o: ..\lvgl\src\font\lv_font.h
.\_build\lv_font.o: ..\lvgl\src\font\../lv_conf_internal.h
.\_build\lv_font.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\lv_font.o: ..\lvgl\lv_conf.h
.\_build\lv_font.o: ..\lvgl\src\font\../lv_conf_kconfig.h
.\_build\lv_font.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\lv_font.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\lv_font.o: ..\lvgl\src\font\lv_symbol_def.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_area.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_utils.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_log.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_types.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_assert.h
.\_build\lv_font.o: ..\lvgl\src\font\../misc/lv_mem.h
.\_build\lv_font.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\string.h
