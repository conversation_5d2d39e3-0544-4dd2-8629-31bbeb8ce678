<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\_build\nrf52840_xxaa.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\_build\nrf52840_xxaa.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Mon Jun 16 20:09:34 2025
<BR><P>
<H3>Maximum Stack Usage =        728 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; comm_ble_init &rArr; services_init &rArr; ble_dis_init &rArr; char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[5]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">NMI_Handler</a><BR>
 <LI><a href="#[6]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">HardFault_Handler</a><BR>
 <LI><a href="#[7]">MemoryManagement_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">MemoryManagement_Handler</a><BR>
 <LI><a href="#[8]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">BusFault_Handler</a><BR>
 <LI><a href="#[9]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">UsageFault_Handler</a><BR>
 <LI><a href="#[a]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">SVC_Handler</a><BR>
 <LI><a href="#[b]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">DebugMon_Handler</a><BR>
 <LI><a href="#[c]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">PendSV_Handler</a><BR>
 <LI><a href="#[d]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[d]">SysTick_Handler</a><BR>
 <LI><a href="#[1d]">CCM_AAR_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1d]">CCM_AAR_IRQHandler</a><BR>
 <LI><a href="#[1cb]">std_n</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a5]">nrf_log_frontend_dequeue</a><BR>
 <LI><a href="#[80]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[80]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</a><BR>
 <LI><a href="#[7f]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7f]">__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</a><BR>
 <LI><a href="#[76]">nrf_fstorage_sys_evt_handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1b2]">queue_process</a><BR>
 <LI><a href="#[11e]">buffer_add</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[11e]">buffer_add</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[8]">BusFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1d]">CCM_AAR_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[21]">COMP_LPCOMP_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[36]">CRYPTOCELL_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1c]">ECB_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[32]">FPU_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[14]">GPIOTE_IRQHandler</a> from nrfx_gpiote.o(i.GPIOTE_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[31]">I2S_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2c]">MWU_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[7]">MemoryManagement_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[13]">NFCT_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2b]">PDM_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[e]">POWER_CLOCK_IRQHandler</a> from nrfx_clock.o(i.POWER_CLOCK_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2a]">PWM0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2d]">PWM1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2e]">PWM2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[37]">PWM3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[20]">QDEC_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[35]">QSPI_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[f]">RADIO_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1b]">RNG_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[19]">RTC0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1f]">RTC1_IRQHandler</a> from drv_rtc.o(i.RTC1_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[30]">RTC2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[15]">SAADC_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[11]">SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</a> from nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[12]">SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[2f]">SPIM2_SPIS2_SPI2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[38]">SPIM3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[22]">SWI0_EGU0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[23]">SWI1_EGU1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[24]">SWI2_EGU2_IRQHandler</a> from nrf_sdh.o(i.SWI2_EGU2_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[25]">SWI3_EGU3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[26]">SWI4_EGU4_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[27]">SWI5_EGU5_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[3a]">SystemInit</a> from system_nrf52.o(i.SystemInit) referenced from arm_startup_nrf52840.o(.text)
 <LI><a href="#[1a]">TEMP_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[16]">TIMER0_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[17]">TIMER1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[18]">TIMER2_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[28]">TIMER3_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[29]">TIMER4_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[10]">UARTE0_UART0_IRQHandler</a> from nrfx_prs.o(i.UARTE0_UART0_IRQHandler) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[34]">UARTE1_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[33]">USBD_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[1e]">WDT_IRQHandler</a> from arm_startup_nrf52840.o(.text) referenced from arm_startup_nrf52840.o(RESET)
 <LI><a href="#[3b]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from arm_startup_nrf52840.o(.text)
 <LI><a href="#[3d]">_snputc</a> from printf3.o(i._snputc) referenced from printf3.o(i.__0snprintf$3)
 <LI><a href="#[4d]">app_error_fault_handler</a> from app_error_weak.o(i.app_error_fault_handler) referenced from nrf_sdh.o(i.nrf_sdh_enable_request)
 <LI><a href="#[46]">apply_pending_handle</a> from gatt_cache_manager.o(i.apply_pending_handle) referenced from gatt_cache_manager.o(i.gcm_ble_evt_handler)
 <LI><a href="#[6e]">ble_advertising_on_ble_evt</a> from ble_advertising.o(i.ble_advertising_on_ble_evt) referenced from ble_conn.o(sdh_ble_observers1)
 <LI><a href="#[71]">ble_bas_on_ble_evt</a> from ble_bas.o(i.ble_bas_on_ble_evt) referenced from ble_conn.o(sdh_ble_observers2)
 <LI><a href="#[72]">ble_dfu_buttonless_on_ble_evt</a> from ble_dfu.o(i.ble_dfu_buttonless_on_ble_evt) referenced from ble_dfu.o(sdh_ble_observers2)
 <LI><a href="#[77]">ble_dfu_buttonless_on_sys_evt</a> from ble_dfu_unbonded.o(i.ble_dfu_buttonless_on_sys_evt) referenced from ble_dfu_unbonded.o(sdh_soc_observers1)
 <LI><a href="#[51]">ble_dfu_evt_handler</a> from ble_conn.o(i.ble_dfu_evt_handler) referenced from ble_conn.o(i.services_init)
 <LI><a href="#[69]">ble_evt_handler</a> from ble_conn_state.o(i.ble_evt_handler) referenced from ble_conn_state.o(sdh_ble_observers0)
 <LI><a href="#[6a]">ble_evt_handler</a> from bsp_btn_ble.o(i.ble_evt_handler) referenced 3 times from bsp_btn_ble.o(sdh_ble_observers1)
 <LI><a href="#[73]">ble_evt_handler</a> from ble_conn.o(i.ble_evt_handler) referenced from ble_conn.o(sdh_ble_observers3)
 <LI><a href="#[6f]">ble_nus_on_ble_evt</a> from ble_nus.o(i.ble_nus_on_ble_evt) referenced from ble_conn.o(sdh_ble_observers2)
 <LI><a href="#[7c]">buttonless_dfu_sdh_state_observer</a> from ble_conn.o(i.buttonless_dfu_sdh_state_observer) referenced from ble_conn.o(sdh_state_observers0)
 <LI><a href="#[56]">car_update_pending_handle</a> from gatt_cache_manager.o(i.car_update_pending_handle) referenced from gatt_cache_manager.o(i.update_pending_flags_check)
 <LI><a href="#[48]">clock_irq_handler</a> from nrf_drv_clock.o(i.clock_irq_handler) referenced from nrf_drv_clock.o(i.nrf_drv_clock_init)
 <LI><a href="#[62]">compare_func</a> from app_timer2.o(i.compare_func) referenced from app_timer2.o(.constdata)
 <LI><a href="#[45]">conn_params_error_handler</a> from ble_conn.o(i.conn_params_error_handler) referenced from ble_conn.o(i.comm_ble_init)
 <LI><a href="#[55]">db_update_pending_handle</a> from gatt_cache_manager.o(i.db_update_pending_handle) referenced from gatt_cache_manager.o(i.update_pending_flags_check)
 <LI><a href="#[41]">disconnect</a> from ble_conn.o(i.disconnect) referenced from ble_conn.o(i.ble_dfu_evt_handler)
 <LI><a href="#[40]">dummy_evt_handler</a> from ble_dfu.o(i.dummy_evt_handler) referenced from ble_dfu.o(i.ble_dfu_buttonless_init)
 <LI><a href="#[3c]">fputc</a> from retarget.o(i.fputc) referenced from printf3.o(i.__0printf$3)
 <LI><a href="#[3]">fs_event_handler</a> from fds.o(i.fs_event_handler) referenced 2 times from fds.o(fs_data)
 <LI><a href="#[42]">gatt_evt_handler</a> from ble_conn.o(i.gatt_evt_handler) referenced from ble_conn.o(i.comm_ble_init)
 <LI><a href="#[58]">gcm_im_evt_handler</a> from gatt_cache_manager.o(i.gcm_im_evt_handler) referenced from id_manager.o(.constdata)
 <LI><a href="#[5d]">gcm_pdb_evt_handler</a> from gatt_cache_manager.o(i.gcm_pdb_evt_handler) referenced from peer_database.o(.constdata)
 <LI><a href="#[5c]">gscm_pdb_evt_handler</a> from gatts_cache_manager.o(i.gscm_pdb_evt_handler) referenced from peer_database.o(.constdata)
 <LI><a href="#[53]">link_secure_pending_handle</a> from security_manager.o(i.link_secure_pending_handle) referenced from security_manager.o(i.sm_ble_evt_handler)
 <LI><a href="#[53]">link_secure_pending_handle</a> from security_manager.o(i.link_secure_pending_handle) referenced from security_manager.o(i.sm_pdb_evt_handler)
 <LI><a href="#[39]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[6d]">nrf_ble_gatt_on_ble_evt</a> from nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt) referenced from ble_conn.o(sdh_ble_observers1)
 <LI><a href="#[70]">nrf_ble_qwr_on_ble_evt</a> from nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt) referenced from ble_conn.o(sdh_ble_observers2)
 <LI><a href="#[74]">nrf_fstorage_sdh_req_handler</a> from nrf_fstorage_sd.o(i.nrf_fstorage_sdh_req_handler) referenced from nrf_fstorage_sd.o(sdh_req_observers0)
 <LI><a href="#[7b]">nrf_fstorage_sdh_state_handler</a> from nrf_fstorage_sd.o(i.nrf_fstorage_sdh_state_handler) referenced from nrf_fstorage_sd.o(sdh_state_observers0)
 <LI><a href="#[76]">nrf_fstorage_sys_evt_handler</a> from nrf_fstorage_sd.o(i.nrf_fstorage_sys_evt_handler) referenced from nrf_fstorage_sd.o(sdh_soc_observers0)
 <LI><a href="#[65]">nrf_log_backend_rtt_flush</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[64]">nrf_log_backend_rtt_panic_set</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[63]">nrf_log_backend_rtt_put</a> from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put) referenced from nrf_log_backend_rtt.o(.constdata)
 <LI><a href="#[68]">nrf_log_backend_uart_flush</a> from nrf_log_backend_uart.o(i.nrf_log_backend_uart_flush) referenced from nrf_log_backend_uart.o(.constdata)
 <LI><a href="#[67]">nrf_log_backend_uart_panic_set</a> from nrf_log_backend_uart.o(i.nrf_log_backend_uart_panic_set) referenced from nrf_log_backend_uart.o(.constdata)
 <LI><a href="#[66]">nrf_log_backend_uart_put</a> from nrf_log_backend_uart.o(i.nrf_log_backend_uart_put) referenced from nrf_log_backend_uart.o(.constdata)
 <LI><a href="#[4f]">nrf_qwr_error_handler</a> from ble_conn.o(i.nrf_qwr_error_handler) referenced from ble_conn.o(i.services_init)
 <LI><a href="#[78]">nrf_sdh_ble_evts_poll</a> from nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll) referenced from nrf_sdh_ble.o(sdh_stack_observers0)
 <LI><a href="#[79]">nrf_sdh_soc_evts_poll</a> from nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll) referenced from nrf_sdh_soc.o(sdh_stack_observers0)
 <LI><a href="#[60]">nrfx_uart_0_irq_handler</a> from nrfx_uart.o(i.nrfx_uart_0_irq_handler) referenced from nrfx_uart.o(.constdata)
 <LI><a href="#[61]">nrfx_uarte_0_irq_handler</a> from nrfx_uarte.o(i.nrfx_uarte_0_irq_handler) referenced from nrfx_uarte.o(.constdata)
 <LI><a href="#[50]">nus_data_handler</a> from ble_conn.o(i.nus_data_handler) referenced from ble_conn.o(i.services_init)
 <LI><a href="#[43]">on_adv_evt</a> from ble_conn.o(i.on_adv_evt) referenced from ble_conn.o(i.comm_ble_init)
 <LI><a href="#[44]">on_conn_params_evt</a> from ble_conn.o(i.on_conn_params_evt) referenced from ble_conn.o(i.comm_ble_init)
 <LI><a href="#[52]">params_reply_pending_handle</a> from security_manager.o(i.params_reply_pending_handle) referenced from security_manager.o(i.sm_ble_evt_handler)
 <LI><a href="#[52]">params_reply_pending_handle</a> from security_manager.o(i.params_reply_pending_handle) referenced from security_manager.o(i.sm_pdb_evt_handler)
 <LI><a href="#[59]">pdb_pds_evt_handler</a> from peer_database.o(i.pdb_pds_evt_handler) referenced from peer_data_storage.o(.constdata)
 <LI><a href="#[1]">pm_gcm_evt_handler</a> from peer_manager.o(i.pm_gcm_evt_handler) referenced 2 times from gatt_cache_manager.o(.data)
 <LI><a href="#[2]">pm_gscm_evt_handler</a> from peer_manager.o(i.pm_gscm_evt_handler) referenced 2 times from gatts_cache_manager.o(.data)
 <LI><a href="#[57]">pm_im_evt_handler</a> from peer_manager.o(i.pm_im_evt_handler) referenced from id_manager.o(.constdata)
 <LI><a href="#[5a]">pm_pdb_evt_handler</a> from peer_manager.o(i.pm_pdb_evt_handler) referenced from peer_database.o(.constdata)
 <LI><a href="#[5f]">pm_sm_evt_handler</a> from peer_manager.o(i.pm_sm_evt_handler) referenced from security_manager.o(.constdata)
 <LI><a href="#[4e]">sc_send_pending_handle</a> from gatt_cache_manager.o(i.sc_send_pending_handle) referenced from gatt_cache_manager.o(i.service_changed_pending_flags_check)
 <LI><a href="#[7a]">sd_state_evt_handler</a> from nrf_drv_clock.o(i.sd_state_evt_handler) referenced from nrf_drv_clock.o(sdh_state_observers0)
 <LI><a href="#[4b]">serial_tx</a> from nrf_log_backend_rtt.o(i.serial_tx) referenced from nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
 <LI><a href="#[4c]">serial_tx</a> from nrf_log_backend_uart.o(i.serial_tx) referenced from nrf_log_backend_uart.o(i.nrf_log_backend_uart_put)
 <LI><a href="#[5b]">sm_pdb_evt_handler</a> from security_manager.o(i.sm_pdb_evt_handler) referenced from peer_database.o(.constdata)
 <LI><a href="#[5e]">sm_smd_evt_handler</a> from security_manager.o(i.sm_smd_evt_handler) referenced from security_dispatcher.o(.constdata)
 <LI><a href="#[75]">soc_evt_handler</a> from nrf_drv_clock.o(i.soc_evt_handler) referenced from nrf_drv_clock.o(sdh_soc_observers0)
 <LI><a href="#[47]">uart_event_handle</a> from ble_conn.o(i.uart_event_handle) referenced from main.o(i.main)
 <LI><a href="#[3e]">uart_event_handler</a> from app_uart_fifo.o(i.uart_event_handler) referenced from app_uart_fifo.o(i.app_uart_init)
 <LI><a href="#[4a]">uart_evt_handler</a> from nrf_drv_uart.o(i.uart_evt_handler) referenced from nrf_drv_uart.o(i.nrf_drv_uart_init)
 <LI><a href="#[54]">uart_evt_handler</a> from nrf_log_backend_uart.o(i.uart_evt_handler) referenced from nrf_log_backend_uart.o(i.uart_init)
 <LI><a href="#[49]">uarte_evt_handler</a> from nrf_drv_uart.o(i.uarte_evt_handler) referenced from nrf_drv_uart.o(i.nrf_drv_uart_init)
 <LI><a href="#[3f]">update_timeout_handler</a> from ble_conn_params.o(i.update_timeout_handler) referenced from ble_conn_params.o(i.ble_conn_params_init)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3b]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(.text)
</UL>
<P><STRONG><a name="[22c]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[7d]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[84]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[22d]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[22e]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[22f]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[230]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[231]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[18d]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
</UL>

<P><STRONG><a name="[7f]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>

<P><STRONG><a name="[190]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
</UL>

<P><STRONG><a name="[80]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>

<P><STRONG><a name="[232]"></a>__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_space_clear</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, nrf_atfifo.o(.emb_text), UNUSED)

<P><STRONG><a name="[199]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
</UL>

<P><STRONG><a name="[198]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_or
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_or
</UL>

<P><STRONG><a name="[196]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_and
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_and
</UL>

<P><STRONG><a name="[233]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_eor</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[195]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_add
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
</UL>

<P><STRONG><a name="[19b]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_sub
</UL>

<P><STRONG><a name="[234]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_cmp_exch</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[235]"></a>__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub_hs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, nrf_atomic.o(.emb_text), UNUSED)

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>MemoryManagement_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemoryManagement_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>CCM_AAR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CCM_AAR_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>COMP_LPCOMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>CRYPTOCELL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ECB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>MWU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>NFCT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>PDM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>PWM0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>PWM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>PWM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>PWM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>QDEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>QSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RADIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>RTC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>RTC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>SAADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPIM2_SPIS2_SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>SPIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>SWI0_EGU0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SWI1_EGU1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>SWI3_EGU3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>SWI4_EGU4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>SWI5_EGU5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>TEMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMER0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>UARTE1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USBD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>WDT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, arm_startup_nrf52840.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1cd]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[236]"></a>___aeabi_memcpy8$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>__aeabi_memcpy</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_data_encode
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;manuf_specific_data_encode
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ctrl_pt_write
</UL>

<P><STRONG><a name="[b5]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_params_init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_modes_config_set
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_list_get
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_proc_start
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_sec_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_evt
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[237]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[238]"></a>__aeabi_memmove4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[239]"></a>__aeabi_memmove8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[82]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[23a]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[23b]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
</UL>

<P><STRONG><a name="[a1]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ah
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptor_add
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_config_req
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_evt
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_get
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_char_add
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;char_add
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;battery_level_char_add
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connect
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_on_ble_evt
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gap_params_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_evt_handler
</UL>

<P><STRONG><a name="[23c]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[11a]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_ascii_to_utf8
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gap_params_init
</UL>

<P><STRONG><a name="[9a]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_master_ids_compare
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_is_duplicate_bonding_data
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_address_resolve
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_compare
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
</UL>

<P><STRONG><a name="[23d]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[7e]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[23e]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[14]"></a>GPIOTE_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, nrfx_gpiote.o(i.GPIOTE_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = GPIOTE_IRQHandler &rArr; port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_is_set
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpiote_event_clear
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>POWER_CLOCK_IRQHandler</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, nrfx_clock.o(i.POWER_CLOCK_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = POWER_CLOCK_IRQHandler &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_check
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>RTC1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, drv_rtc.o(i.RTC1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>SEGGER_RTT_WriteNoLock</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, segger_rtt.o(i.SEGGER_RTT_WriteNoLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_tx
</UL>

<P><STRONG><a name="[11]"></a>SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, nrfx_spim.o(i.SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler &rArr; nrf_gpio_pin_set
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_spim_event_check
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>SWI2_EGU2_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_sdh.o(i.SWI2_EGU2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SWI2_EGU2_IRQHandler &rArr; nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SystemInit</STRONG> (Thumb, 414 bytes, Stack size 12 bytes, system_nrf52.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SystemInit
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvmc_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(.text)
</UL>
<P><STRONG><a name="[10]"></a>UARTE0_UART0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrfx_prs.o(i.UARTE0_UART0_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> arm_startup_nrf52840.o(RESET)
</UL>
<P><STRONG><a name="[95]"></a>__0printf$3</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[241]"></a>__1printf$3</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3), UNUSED)

<P><STRONG><a name="[21b]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf3.o(i.__0printf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[97]"></a>__0snprintf$3</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, printf3.o(i.__0snprintf$3), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[242]"></a>__1snprintf$3</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printf3.o(i.__0snprintf$3), UNUSED)

<P><STRONG><a name="[14f]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printf3.o(i.__0snprintf$3))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gap_params_init
</UL>

<P><STRONG><a name="[243]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[244]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[245]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[99]"></a>addr_compare</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, id_manager.o(i.addr_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = addr_compare &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_is_duplicate_bonding_data
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
</UL>

<P><STRONG><a name="[9d]"></a>advertising_start</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ble_conn.o(i.advertising_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = advertising_start &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[a0]"></a>ah</STRONG> (Thumb, 82 bytes, Stack size 64 bytes, id_manager.o(i.ah))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ah
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_address_resolve
</UL>

<P><STRONG><a name="[4d]"></a>app_error_fault_handler</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, app_error_weak.o(i.app_error_fault_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_panic
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh.o(i.nrf_sdh_enable_request)
</UL>
<P><STRONG><a name="[9f]"></a>app_error_handler_bare</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, app_error.o(i.app_error_handler_bare))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handle
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_get
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevice_evt_irq_disable
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_tx
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_id_encode
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnp_id_encode
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;char_add
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_start
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_conn_params_evt
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_qwr_error_handler
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gap_params_init
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_params_error_handler
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[a7]"></a>app_fifo_get</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, app_fifo.o(i.app_fifo_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_fifo_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_get
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[b4]"></a>app_fifo_init</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, app_fifo.o(i.app_fifo_init))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
</UL>

<P><STRONG><a name="[a9]"></a>app_fifo_put</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_fifo.o(i.app_fifo_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_fifo_put
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_put
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[185]"></a>app_sched_init</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, app_scheduler.o(i.app_sched_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = app_sched_init
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ed]"></a>app_timer_create</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, app_timer2.o(i.app_timer_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_timer_create
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_params_init
</UL>

<P><STRONG><a name="[ab]"></a>app_timer_start</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, app_timer2.o(i.app_timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_params_negotiation
</UL>

<P><STRONG><a name="[b0]"></a>app_timer_stop</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, app_timer2.o(i.app_timer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = app_timer_stop &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[b1]"></a>app_uart_get</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, app_uart_fifo.o(i.app_uart_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = app_uart_get &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handle
</UL>

<P><STRONG><a name="[b3]"></a>app_uart_init</STRONG> (Thumb, 148 bytes, Stack size 56 bytes, app_uart_fifo.o(i.app_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = app_uart_init &rArr; nrf_drv_uart_init &rArr; nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b7]"></a>app_uart_put</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, app_uart_fifo.o(i.app_uart_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = app_uart_put &rArr; nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx_in_progress
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx_in_progress
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[ac]"></a>app_util_critical_region_enter</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, app_util_platform.o(i.app_util_critical_region_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>

<P><STRONG><a name="[ad]"></a>app_util_critical_region_exit</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, app_util_platform.o(i.app_util_critical_region_exit))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>

<P><STRONG><a name="[d8]"></a>blcm_link_ctx_get</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, ble_link_ctx_manager.o(i.blcm_link_ctx_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = blcm_link_ctx_get &rArr; ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_conn_idx
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connect
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_on_ble_evt
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_data_send
</UL>

<P><STRONG><a name="[da]"></a>ble_advdata_encode</STRONG> (Thumb, 386 bytes, Stack size 40 bytes, ble_advdata.o(i.ble_advdata_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ble_advdata_encode &rArr; uuid_list_encode &rArr; uuid_list_sized_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uuid_list_encode
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uint16_encode
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_data_encode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;name_encode
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;manuf_specific_data_encode
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_int_encode
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_device_addr_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_init
</UL>

<P><STRONG><a name="[e2]"></a>ble_advdata_parse</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, ble_advdata.o(i.ble_advdata_parse))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ble_advdata_parse &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_search
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flags_set
</UL>

<P><STRONG><a name="[e3]"></a>ble_advdata_search</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ble_advdata.o(i.ble_advdata_search))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_advdata_search
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_parse
</UL>

<P><STRONG><a name="[12f]"></a>ble_advertising_conn_cfg_tag_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ble_advertising.o(i.ble_advertising_conn_cfg_tag_set))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[e4]"></a>ble_advertising_init</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, ble_advertising.o(i.ble_advertising_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = ble_advertising_init &rArr; ble_advdata_encode &rArr; uuid_list_encode &rArr; uuid_list_sized_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adv_set_data_size_max_get
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[e6]"></a>ble_advertising_modes_config_set</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ble_advertising.o(i.ble_advertising_modes_config_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_advertising_modes_config_set &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_evt_handler
</UL>

<P><STRONG><a name="[6e]"></a>ble_advertising_on_ble_evt</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, ble_advertising.o(i.ble_advertising_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = ble_advertising_on_ble_evt &rArr; ble_advertising_start &rArr; flags_set &rArr; ble_advdata_parse &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[9e]"></a>ble_advertising_start</STRONG> (Thumb, 560 bytes, Stack size 40 bytes, ble_advertising.o(i.ble_advertising_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = ble_advertising_start &rArr; flags_set &rArr; ble_advdata_parse &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;use_whitelist
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;phy_is_valid
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flags_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_on_ble_evt
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_start
</UL>

<P><STRONG><a name="[ea]"></a>ble_bas_init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ble_bas.o(i.ble_bas_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ble_bas_init &rArr; battery_level_char_add &rArr; characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;battery_level_char_add
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[71]"></a>ble_bas_on_ble_evt</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, ble_bas.o(i.ble_bas_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_bas_on_ble_evt
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_is_notification_enabled
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers2)
</UL>
<P><STRONG><a name="[ec]"></a>ble_conn_params_init</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, ble_conn_params.o(i.ble_conn_params_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ble_conn_params_init &rArr; app_timer_create
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_create
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[d9]"></a>ble_conn_state_conn_idx</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, ble_conn_state.o(i.ble_conn_state_conn_idx))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;blcm_link_ctx_get
</UL>

<P><STRONG><a name="[ef]"></a>ble_conn_state_encrypted</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ble_conn_state.o(i.ble_conn_state_encrypted))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_conn_state_encrypted
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_req_process
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[f1]"></a>ble_conn_state_for_each_connected</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ble_conn_state.o(i.ble_conn_state_for_each_connected))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_conn_state_for_each_connected &rArr; for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;for_each_set_flag
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_evt_handler
</UL>

<P><STRONG><a name="[f3]"></a>ble_conn_state_for_each_set_user_flag</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ble_conn_state.o(i.ble_conn_state_for_each_set_user_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ble_conn_state_for_each_set_user_flag &rArr; for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_flag_is_acquired
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;for_each_set_flag
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_pending_flags_check
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_flags_check
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_ble_evt_handler
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_pdb_evt_handler
</UL>

<P><STRONG><a name="[f5]"></a>ble_conn_state_lesc</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ble_conn_state.o(i.ble_conn_state_lesc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_conn_state_lesc
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
</UL>

<P><STRONG><a name="[f6]"></a>ble_conn_state_mitm_protected</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ble_conn_state.o(i.ble_conn_state_mitm_protected))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ble_conn_state_mitm_protected
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
</UL>

<P><STRONG><a name="[f7]"></a>ble_conn_state_role</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, ble_conn_state.o(i.ble_conn_state_role))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_conn_state_role
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_link_secure
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[f8]"></a>ble_conn_state_status</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, ble_conn_state.o(i.ble_conn_state_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_conn_state_status
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
</UL>

<P><STRONG><a name="[a3]"></a>ble_conn_state_user_flag_get</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, ble_conn_state.o(i.ble_conn_state_user_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_conn_state_user_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_flag_is_acquired
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sc_send_pending_handle
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_failure
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;allow_repairing
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_pending_handle
</UL>

<P><STRONG><a name="[c5]"></a>ble_conn_state_user_flag_set</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ble_conn_state.o(i.ble_conn_state_user_flag_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_flag_is_acquired
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flag_toggle
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_im_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_pending_handle
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_needed
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_proc_start
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_sec_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flags_set_from_err_code
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[ee]"></a>ble_conn_state_valid</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ble_conn_state.o(i.ble_conn_state_valid))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_addr_get
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_conn_handle_get
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_status
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_role
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_mitm_protected
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_lesc
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_encrypted
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_conn_idx
</UL>

<P><STRONG><a name="[ff]"></a>ble_dfu_buttonless_backend_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ble_dfu_unbonded.o(i.ble_dfu_buttonless_backend_init))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_init
</UL>

<P><STRONG><a name="[fa]"></a>ble_dfu_buttonless_bootloader_start_finalize</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, ble_dfu.o(i.ble_dfu_buttonless_bootloader_start_finalize))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ble_dfu_buttonless_bootloader_start_finalize &rArr; nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_bootloader_start_prepare
</UL>

<P><STRONG><a name="[fc]"></a>ble_dfu_buttonless_bootloader_start_prepare</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ble_dfu_unbonded.o(i.ble_dfu_buttonless_bootloader_start_prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = ble_dfu_buttonless_bootloader_start_prepare &rArr; ble_dfu_buttonless_bootloader_start_finalize &rArr; nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_bootloader_start_finalize
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ble_evt
</UL>

<P><STRONG><a name="[fd]"></a>ble_dfu_buttonless_char_add</STRONG> (Thumb, 80 bytes, Stack size 40 bytes, ble_dfu_unbonded.o(i.ble_dfu_buttonless_char_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ble_dfu_buttonless_char_add &rArr; characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_init
</UL>

<P><STRONG><a name="[fe]"></a>ble_dfu_buttonless_init</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, ble_dfu.o(i.ble_dfu_buttonless_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ble_dfu_buttonless_init &rArr; ble_dfu_buttonless_char_add &rArr; characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_char_add
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_backend_init
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[72]"></a>ble_dfu_buttonless_on_ble_evt</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, ble_dfu.o(i.ble_dfu_buttonless_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ble_dfu_buttonless_on_ble_evt &rArr; ble_dfu_buttonless_bootloader_start_prepare &rArr; ble_dfu_buttonless_bootloader_start_finalize &rArr; nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_bootloader_start_prepare
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_ctrlpt_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_dfu.o(sdh_ble_observers2)
</UL>
<P><STRONG><a name="[101]"></a>ble_dfu_buttonless_on_ctrl_pt_write</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, ble_dfu_unbonded.o(i.ble_dfu_buttonless_on_ctrl_pt_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = ble_dfu_buttonless_on_ctrl_pt_write &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_resp_send
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_ctrlpt_write
</UL>

<P><STRONG><a name="[77]"></a>ble_dfu_buttonless_on_sys_evt</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, ble_dfu_unbonded.o(i.ble_dfu_buttonless_on_sys_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ble_dfu_buttonless_on_sys_evt &rArr; ble_dfu_buttonless_resp_send
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_resp_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_dfu_unbonded.o(sdh_soc_observers1)
</UL>
<P><STRONG><a name="[102]"></a>ble_dfu_buttonless_resp_send</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, ble_dfu.o(i.ble_dfu_buttonless_resp_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ble_dfu_buttonless_resp_send
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ctrl_pt_write
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_sys_evt
</UL>

<P><STRONG><a name="[104]"></a>ble_dis_init</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, ble_dis.o(i.ble_dis_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ble_dis_init &rArr; char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_id_encode
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnp_id_encode
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;char_add
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[115]"></a>ble_nus_data_send</STRONG> (Thumb, 102 bytes, Stack size 40 bytes, ble_nus.o(i.ble_nus_data_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ble_nus_data_send &rArr; blcm_link_ctx_get &rArr; ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;blcm_link_ctx_get
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handle
</UL>

<P><STRONG><a name="[116]"></a>ble_nus_init</STRONG> (Thumb, 206 bytes, Stack size 72 bytes, ble_nus.o(i.ble_nus_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ble_nus_init &rArr; characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[6f]"></a>ble_nus_on_ble_evt</STRONG> (Thumb, 118 bytes, Stack size 48 bytes, ble_nus.o(i.ble_nus_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ble_nus_on_ble_evt &rArr; on_connect &rArr; blcm_link_ctx_get &rArr; ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;blcm_link_ctx_get
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connect
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers2)
</UL>
<P><STRONG><a name="[119]"></a>ble_srv_ascii_to_utf8</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, ble_srv_common.o(i.ble_srv_ascii_to_utf8))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ble_srv_ascii_to_utf8
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[1eb]"></a>ble_srv_is_indication_enabled</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ble_srv_common.o(i.ble_srv_is_indication_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_ctrlpt_write
</UL>

<P><STRONG><a name="[eb]"></a>ble_srv_is_notification_enabled</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ble_srv_common.o(i.ble_srv_is_notification_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_write
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connect
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_bas_on_ble_evt
</UL>

<P><STRONG><a name="[d6]"></a>ble_srv_report_ref_encode</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ble_srv_common.o(i.ble_srv_report_ref_encode))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;battery_level_char_add
</UL>

<P><STRONG><a name="[9c]"></a>bsp_event_to_button_action_assign</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, bsp.o(i.bsp_event_to_button_action_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
</UL>

<P><STRONG><a name="[1c8]"></a>buffer_is_empty</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.buffer_is_empty))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[d5]"></a>characteristic_add</STRONG> (Thumb, 400 bytes, Stack size 96 bytes, ble_srv_common.o(i.characteristic_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_security_req
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_char_add
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;char_add
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;battery_level_char_add
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_init
</UL>

<P><STRONG><a name="[127]"></a>comm_ble_init</STRONG> (Thumb, 230 bytes, Stack size 184 bytes, ble_conn.o(i.comm_ble_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 688<LI>Call Chain = comm_ble_init &rArr; services_init &rArr; ble_dis_init &rArr; char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_params_init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_conn_cfg_tag_set
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_att_mtu_periph_set
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_start
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gap_params_init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>conn_handle_list_get</STRONG> (Thumb, 62 bytes, Stack size 72 bytes, ble_conn_state.o(i.conn_handle_list_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = conn_handle_list_get &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[d7]"></a>descriptor_add</STRONG> (Thumb, 170 bytes, Stack size 48 bytes, ble_srv_common.o(i.descriptor_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = descriptor_add
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_security_req
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;battery_level_char_add
</UL>

<P><STRONG><a name="[163]"></a>drv_rtc_counter_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_counter_get))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_now
</UL>

<P><STRONG><a name="[222]"></a>drv_rtc_irq_trigger</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, drv_rtc.o(i.drv_rtc_irq_trigger))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
</UL>

<P><STRONG><a name="[141]"></a>fds_file_delete</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, fds.o(i.fds_file_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_start
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_store
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_get
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
</UL>

<P><STRONG><a name="[145]"></a>fds_record_close</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, fds.o(i.fds_record_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = fds_record_close &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
</UL>

<P><STRONG><a name="[146]"></a>fds_record_find</STRONG> (Thumb, 14 bytes, Stack size 24 bytes, fds.o(i.fds_record_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_find
</UL>

<P><STRONG><a name="[147]"></a>fds_record_find_by_key</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, fds.o(i.fds_record_find_by_key))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = fds_record_find_by_key &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
</UL>

<P><STRONG><a name="[148]"></a>fds_record_find_in_file</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, fds.o(i.fds_record_find_in_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = fds_record_find_in_file &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
</UL>

<P><STRONG><a name="[1ff]"></a>fds_record_id_from_desc</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fds.o(i.fds_record_id_from_desc))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
</UL>

<P><STRONG><a name="[149]"></a>fds_record_open</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, fds.o(i.fds_record_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = fds_record_open &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
</UL>

<P><STRONG><a name="[14a]"></a>fds_record_update</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fds.o(i.fds_record_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = fds_record_update &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
</UL>

<P><STRONG><a name="[14c]"></a>fds_record_write</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fds.o(i.fds_record_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
</UL>

<P><STRONG><a name="[3c]"></a>fputc</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, retarget.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fputc &rArr; app_uart_put &rArr; nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0printf$3)
</UL>
<P><STRONG><a name="[112]"></a>gcm_ble_evt_handler</STRONG> (Thumb, 358 bytes, Stack size 48 bytes, gatt_cache_manager.o(i.gcm_ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = gcm_ble_evt_handler &rArr; store_car_value &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_db_change_notification_done
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_pending_flags_check
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_flags_check
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[58]"></a>gcm_im_evt_handler</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gatt_cache_manager.o(i.gcm_im_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = gcm_im_evt_handler &rArr; local_db_apply_in_evt &rArr; gscm_local_db_cache_apply &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_needed
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_pending_flags_check
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_needed
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> id_manager.o(.constdata)
</UL>
<P><STRONG><a name="[5d]"></a>gcm_pdb_evt_handler</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, gatt_cache_manager.o(i.gcm_pdb_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = gcm_pdb_evt_handler &rArr; car_update_needed &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_conn_handle_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_pending_flags_check
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_flags_check
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_needed
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_database.o(.constdata)
</UL>
<P><STRONG><a name="[15c]"></a>gscm_db_change_notification_done</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, gatts_cache_manager.o(i.gscm_db_change_notification_done))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = gscm_db_change_notification_done &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
</UL>

<P><STRONG><a name="[165]"></a>gscm_local_db_cache_apply</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, gatts_cache_manager.o(i.gscm_local_db_cache_apply))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = gscm_local_db_cache_apply &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
</UL>

<P><STRONG><a name="[166]"></a>gscm_local_db_cache_update</STRONG> (Thumb, 260 bytes, Stack size 48 bytes, gatts_cache_manager.o(i.gscm_local_db_cache_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = gscm_local_db_cache_update &rArr; pdb_write_buf_store &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_store
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_release
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
</UL>

<P><STRONG><a name="[5c]"></a>gscm_pdb_evt_handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gatts_cache_manager.o(i.gscm_pdb_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = gscm_pdb_evt_handler &rArr; service_changed_pending_set &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_database.o(.constdata)
</UL>
<P><STRONG><a name="[161]"></a>gscm_service_changed_ind_needed</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, gatts_cache_manager.o(i.gscm_service_changed_ind_needed))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = gscm_service_changed_ind_needed &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_im_evt_handler
</UL>

<P><STRONG><a name="[169]"></a>gscm_service_changed_ind_send</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, gatts_cache_manager.o(i.gscm_service_changed_ind_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = gscm_service_changed_ind_send &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
</UL>

<P><STRONG><a name="[16a]"></a>im_address_resolve</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, id_manager.o(i.im_address_resolve))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = im_address_resolve &rArr; ah
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ah
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
</UL>

<P><STRONG><a name="[16b]"></a>im_ble_addr_get</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, id_manager.o(i.im_ble_addr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = im_ble_addr_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
</UL>

<P><STRONG><a name="[110]"></a>im_ble_evt_handler</STRONG> (Thumb, 210 bytes, Stack size 80 bytes, id_manager.o(i.im_ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = im_ble_evt_handler &rArr; pds_peer_data_iterate &rArr; fds_record_open &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate_prepare
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_address_resolve
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_compare
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[140]"></a>im_conn_handle_get</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, id_manager.o(i.im_conn_handle_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = im_conn_handle_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_free
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
</UL>

<P><STRONG><a name="[ca]"></a>im_find_duplicate_bonding_data</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, id_manager.o(i.im_find_duplicate_bonding_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = im_find_duplicate_bonding_data &rArr; pds_peer_data_iterate &rArr; fds_record_open &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate_prepare
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_is_duplicate_bonding_data
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[16e]"></a>im_is_duplicate_bonding_data</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, id_manager.o(i.im_is_duplicate_bonding_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = im_is_duplicate_bonding_data &rArr; addr_compare &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_valid_irk
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_compare
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_find_duplicate_bonding_data
</UL>

<P><STRONG><a name="[171]"></a>im_master_id_is_valid</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, id_manager.o(i.im_master_id_is_valid))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_master_ids_compare
</UL>

<P><STRONG><a name="[170]"></a>im_master_ids_compare</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, id_manager.o(i.im_master_ids_compare))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = im_master_ids_compare &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_master_id_is_valid
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_master_id
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
</UL>

<P><STRONG><a name="[cb]"></a>im_new_peer_id</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, id_manager.o(i.im_new_peer_id))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[d2]"></a>im_peer_free</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, id_manager.o(i.im_peer_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = im_peer_free &rArr; pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_conn_handle_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[c9]"></a>im_peer_id_get_by_conn_handle</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, id_manager.o(i.im_peer_id_get_by_conn_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_needed
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_apply
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_needed
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_evt
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
</UL>

<P><STRONG><a name="[173]"></a>im_peer_id_get_by_master_id</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, id_manager.o(i.im_peer_id_get_by_master_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = im_peer_id_get_by_master_id &rArr; pds_peer_data_iterate &rArr; fds_record_open &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate_prepare
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_master_ids_compare
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
</UL>

<P><STRONG><a name="[16f]"></a>is_valid_irk</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, id_manager.o(i.is_valid_irk))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_is_duplicate_bonding_data
</UL>

<P><STRONG><a name="[221]"></a>log_pending_hook</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.log_pending_hook))
<BR><BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[39]"></a>main</STRONG> (Thumb, 96 bytes, Stack size 40 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = main &rArr; comm_ble_init &rArr; services_init &rArr; ble_dis_init &rArr; char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_sched_init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[18b]"></a>next_id_get</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, peer_id.o(i.next_id_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = next_id_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_used
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_deleted
</UL>

<P><STRONG><a name="[18c]"></a>nrf_atfifo_item_alloc</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_get
</UL>

<P><STRONG><a name="[18e]"></a>nrf_atfifo_item_free</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_free
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_free
</UL>

<P><STRONG><a name="[18f]"></a>nrf_atfifo_item_get</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nrf_atfifo.o(i.nrf_atfifo_item_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atfifo_item_get
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_rspace_req
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[191]"></a>nrf_atfifo_item_put</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, nrf_atfifo.o(i.nrf_atfifo_item_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atfifo_item_put
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atfifo_c_51f461e1__nrf_atfifo_wspace_close
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_req_schedule
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_store
</UL>

<P><STRONG><a name="[10e]"></a>nrf_atflags_clear</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_atflags.o(i.nrf_atflags_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_and
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flag_toggle
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;release
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_release
</UL>

<P><STRONG><a name="[125]"></a>nrf_atflags_fetch_set</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, nrf_atflags.o(i.nrf_atflags_fetch_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_or
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_find_and_set_flag
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;claim
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_block_acquire
</UL>

<P><STRONG><a name="[124]"></a>nrf_atflags_find_and_set_flag</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, nrf_atflags.o(i.nrf_atflags_find_and_set_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_atflags_find_and_set_flag &rArr; nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_fetch_set
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;claim
</UL>

<P><STRONG><a name="[f0]"></a>nrf_atflags_get</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_atflags.o(i.nrf_atflags_get))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_flag_is_acquired
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;for_each_set_flag
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_list_get
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_valid
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_status
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_role
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_mitm_protected
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_lesc
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_encrypted
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mutex_lock_status_get
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;next_id_get
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_is_deleted
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_is_allocated
</UL>

<P><STRONG><a name="[10f]"></a>nrf_atflags_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrf_atflags.o(i.nrf_atflags_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_or
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flag_toggle
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[183]"></a>nrf_atomic_flag_clear_fetch</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_atomic.o(i.nrf_atomic_flag_clear_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_flag_clear_fetch &rArr; nrf_atomic_u32_fetch_and
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_and
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[181]"></a>nrf_atomic_flag_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_atomic.o(i.nrf_atomic_flag_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_or
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[11c]"></a>nrf_atomic_u32_add</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_add
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_open
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[10c]"></a>nrf_atomic_u32_and</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_clear
</UL>

<P><STRONG><a name="[197]"></a>nrf_atomic_u32_fetch_add</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_add
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_add
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_start
</UL>

<P><STRONG><a name="[194]"></a>nrf_atomic_u32_fetch_and</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_and
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_and
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_clear_fetch
</UL>

<P><STRONG><a name="[192]"></a>nrf_atomic_u32_fetch_or</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_or))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_fetch_set
</UL>

<P><STRONG><a name="[132]"></a>nrf_atomic_u32_fetch_store</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_fetch_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_fetch_store
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_mov
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;db_update_pending_handle
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dropped_sat16_get
</UL>

<P><STRONG><a name="[193]"></a>nrf_atomic_u32_or</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_or))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_orr
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_set
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_set
</UL>

<P><STRONG><a name="[19a]"></a>nrf_atomic_u32_sub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrf_atomic.o(i.nrf_atomic_u32_sub))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_atomic_u32_sub
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___12_nrf_atomic_c_85ca2469__nrf_atomic_internal_sub
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
</UL>

<P><STRONG><a name="[19c]"></a>nrf_balloc_alloc</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_alloc &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>

<P><STRONG><a name="[19d]"></a>nrf_balloc_free</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_balloc.o(i.nrf_balloc_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>

<P><STRONG><a name="[1d3]"></a>nrf_balloc_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, nrf_balloc.o(i.nrf_balloc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
</UL>

<P><STRONG><a name="[12d]"></a>nrf_ble_gatt_att_mtu_periph_set</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_att_mtu_periph_set))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[12c]"></a>nrf_ble_gatt_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_ble_gatt_init
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[6d]"></a>nrf_ble_gatt_on_ble_evt</STRONG> (Thumb, 270 bytes, Stack size 96 bytes, nrf_ble_gatt.o(i.nrf_ble_gatt_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = nrf_ble_gatt_on_ble_evt &rArr; on_connected_evt &rArr; data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[114]"></a>nrf_ble_qwr_conn_handle_assign</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_ble_qwr.o(i.nrf_ble_qwr_conn_handle_assign))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[21c]"></a>nrf_ble_qwr_init</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, nrf_ble_qwr.o(i.nrf_ble_qwr_init))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;services_init
</UL>

<P><STRONG><a name="[70]"></a>nrf_ble_qwr_on_ble_evt</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, nrf_ble_qwr.o(i.nrf_ble_qwr_on_ble_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_ble_qwr_on_ble_evt &rArr; user_mem_reply
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;user_mem_reply
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers2)
</UL>
<P><STRONG><a name="[1a2]"></a>nrf_drv_clock_init</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_drv_clock_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_init
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[1a7]"></a>nrf_drv_clock_lfclk_release</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.nrf_drv_clock_lfclk_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_wdt_started
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[b6]"></a>nrf_drv_uart_init</STRONG> (Thumb, 90 bytes, Stack size 56 bytes, nrf_drv_uart.o(i.nrf_drv_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_drv_uart_init &rArr; nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[1af]"></a>nrf_fprintf</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrf_fprintf.o(i.nrf_fprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[11f]"></a>nrf_fprintf_buffer_flush</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nrf_fprintf.o(i.nrf_fprintf_buffer_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_fprintf_buffer_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
</UL>

<P><STRONG><a name="[1b0]"></a>nrf_fprintf_fmt</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, nrf_fprintf_format.o(i.nrf_fprintf_fmt))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
</UL>

<P><STRONG><a name="[158]"></a>nrf_fstorage_erase</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, nrf_fstorage.o(i.nrf_fstorage_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = nrf_fstorage_erase &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_is_within_bounds
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_execute
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_page_erase
</UL>

<P><STRONG><a name="[74]"></a>nrf_fstorage_sdh_req_handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_fstorage_sd.o(i.nrf_fstorage_sdh_req_handler))
<BR>[Address Reference Count : 1]<UL><LI> nrf_fstorage_sd.o(sdh_req_observers0)
</UL>
<P><STRONG><a name="[7b]"></a>nrf_fstorage_sdh_state_handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, nrf_fstorage_sd.o(i.nrf_fstorage_sdh_state_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = nrf_fstorage_sdh_state_handler &rArr; queue_process &rArr; nrf_fstorage_sys_evt_handler &rArr;  queue_process (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_fstorage_sd.o(sdh_state_observers0)
</UL>
<P><STRONG><a name="[76]"></a>nrf_fstorage_sys_evt_handler</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, nrf_fstorage_sd.o(i.nrf_fstorage_sys_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 320 + In Cycle
<LI>Call Chain = nrf_fstorage_sys_evt_handler &rArr;  queue_process (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_request_continue
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_free
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_fstorage_sd.o(sdh_soc_observers0)
</UL>
<P><STRONG><a name="[159]"></a>nrf_fstorage_write</STRONG> (Thumb, 242 bytes, Stack size 40 bytes, nrf_fstorage.o(i.nrf_fstorage_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_is_within_bounds
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;addr_is_aligned32
</UL>
<BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_execute
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_header_flag_dirty
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_swap
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_data
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_copy
</UL>

<P><STRONG><a name="[1bf]"></a>nrf_log_backend_serial_put</STRONG> (Thumb, 200 bytes, Stack size 104 bytes, nrf_log_backend_serial.o(i.nrf_log_backend_serial_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = nrf_log_backend_serial_put &rArr; nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_rtt_put
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_uart_put
</UL>

<P><STRONG><a name="[20e]"></a>nrf_log_color_id_get</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_color_id_get))
<BR><BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
</UL>

<P><STRONG><a name="[a5]"></a>nrf_log_frontend_dequeue</STRONG> (Thumb, 528 bytes, Stack size 88 bytes, nrf_log_frontend.o(i.nrf_log_frontend_dequeue))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_get
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_is_empty
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invalid_packets_omit
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[a6]"></a>nrf_log_frontend_std_0</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ctrl_pt_write
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nus_data_handler
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_evt_handler
</UL>

<P><STRONG><a name="[103]"></a>nrf_log_frontend_std_1</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_send
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_apply
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_evt_handler
</UL>

<P><STRONG><a name="[c8]"></a>nrf_log_frontend_std_2</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_peer_ranks_get
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reattempt_previous_operations
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gatt_evt_handler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
</UL>

<P><STRONG><a name="[d0]"></a>nrf_log_frontend_std_3</STRONG> (Thumb, 18 bytes, Stack size 24 bytes, nrf_log_frontend.o(i.nrf_log_frontend_std_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_erase
</UL>

<P><STRONG><a name="[1c3]"></a>nrf_log_hexdump_entry_process</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, nrf_log_str_formatter.o(i.nrf_log_hexdump_entry_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = nrf_log_hexdump_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[184]"></a>nrf_log_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.nrf_log_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_log_init &rArr; nrf_memobj_pool_init &rArr; nrf_balloc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ringbuf_init
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_pool_init
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[189]"></a>nrf_log_module_cnt_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.nrf_log_module_cnt_get))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_idx_get
</UL>

<P><STRONG><a name="[1d1]"></a>nrf_log_module_name_get</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_module_name_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrf_log_module_name_get &rArr; module_idx_get
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;module_idx_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
</UL>

<P><STRONG><a name="[a4]"></a>nrf_log_panic</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, nrf_log_frontend.o(i.nrf_log_panic))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_log_panic
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_fault_handler
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
</UL>

<P><STRONG><a name="[1c2]"></a>nrf_log_std_entry_process</STRONG> (Thumb, 182 bytes, Stack size 48 bytes, nrf_log_str_formatter.o(i.nrf_log_std_entry_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prefix_process
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;postfix_process
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[1c9]"></a>nrf_memobj_alloc</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = nrf_memobj_alloc &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_alloc
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[1d2]"></a>nrf_memobj_free</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, nrf_memobj.o(i.nrf_memobj_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_free
</UL>
<BR>[Called By]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_put
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_alloc
</UL>

<P><STRONG><a name="[1c0]"></a>nrf_memobj_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrf_memobj.o(i.nrf_memobj_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_memobj_get &rArr; nrf_atomic_u32_add
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[1cf]"></a>nrf_memobj_pool_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, nrf_memobj.o(i.nrf_memobj_pool_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_memobj_pool_init &rArr; nrf_balloc_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_balloc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
</UL>

<P><STRONG><a name="[1c4]"></a>nrf_memobj_put</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, nrf_memobj.o(i.nrf_memobj_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = nrf_memobj_put &rArr; nrf_memobj_free &rArr; nrf_balloc_free &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_sub
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_free
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[1c1]"></a>nrf_memobj_read</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_memobj_read &rArr; memobj_op &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>

<P><STRONG><a name="[1ca]"></a>nrf_memobj_write</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, nrf_memobj.o(i.nrf_memobj_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_memobj_write &rArr; memobj_op &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memobj_op
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
</UL>

<P><STRONG><a name="[186]"></a>nrf_pwr_mgmt_run</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_pwr_mgmt_run &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fb]"></a>nrf_pwr_mgmt_shutdown</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, nrf_pwr_mgmt.o(i.nrf_pwr_mgmt_shutdown))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_bootloader_start_finalize
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buttonless_dfu_sdh_state_observer
</UL>

<P><STRONG><a name="[1d0]"></a>nrf_ringbuf_init</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, nrf_ringbuf.o(i.nrf_ringbuf_init))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_init
</UL>

<P><STRONG><a name="[1d5]"></a>nrf_sdh_ble_app_ram_start_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_app_ram_start_get))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
</UL>

<P><STRONG><a name="[129]"></a>nrf_sdh_ble_default_cfg_set</STRONG> (Thumb, 226 bytes, Stack size 32 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_default_cfg_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = nrf_sdh_ble_default_cfg_set &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_app_ram_start_get
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[12a]"></a>nrf_sdh_ble_enable</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = nrf_sdh_ble_enable &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[1d8]"></a>nrf_sdh_disable_request</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, nrf_sdh.o(i.nrf_sdh_disable_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = nrf_sdh_disable_request &rArr; softdevice_evt_irq_disable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevice_evt_irq_disable
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_request_continue
</UL>

<P><STRONG><a name="[128]"></a>nrf_sdh_enable_request</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, nrf_sdh.o(i.nrf_sdh_enable_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = nrf_sdh_enable_request &rArr; softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_request_continue
</UL>

<P><STRONG><a name="[92]"></a>nrf_sdh_evts_poll</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, nrf_sdh.o(i.nrf_sdh_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nrf_sdh_evts_poll &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SWI2_EGU2_IRQHandler
</UL>

<P><STRONG><a name="[1a4]"></a>nrf_sdh_is_enabled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, nrf_sdh.o(i.nrf_sdh_is_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_run
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
</UL>

<P><STRONG><a name="[1b4]"></a>nrf_sdh_request_continue</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrf_sdh.o(i.nrf_sdh_request_continue))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = nrf_sdh_request_continue &rArr; nrf_sdh_enable_request &rArr; softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sys_evt_handler
</UL>

<P><STRONG><a name="[1d6]"></a>nrf_section_iter_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_init))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>

<P><STRONG><a name="[1d7]"></a>nrf_section_iter_next</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_section_iter.o(i.nrf_section_iter_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shutdown_process
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_state_observer_notify
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdh_request_observer_notify
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_soc_evts_poll
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_evts_poll
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_evts_poll
</UL>

<P><STRONG><a name="[1de]"></a>nrf_strerror_find</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, nrf_strerror.o(i.nrf_strerror_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_strerror_find
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
</UL>

<P><STRONG><a name="[c7]"></a>nrf_strerror_get</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, nrf_strerror.o(i.nrf_strerror_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = nrf_strerror_get &rArr; nrf_strerror_find
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_find
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_exchange_mtu_request_evt
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_send
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_peer_ranks_get
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reattempt_previous_operations
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_ble_default_cfg_set
</UL>

<P><STRONG><a name="[1a5]"></a>nrfx_clock_enable</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_state_evt_handler
</UL>

<P><STRONG><a name="[1a3]"></a>nrfx_clock_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrfx_clock_init))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[1a8]"></a>nrfx_clock_lfclk_stop</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrfx_clock_lfclk_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
</UL>

<P><STRONG><a name="[1df]"></a>nrfx_prs_acquire</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, nrfx_prs.o(i.nrfx_prs_acquire))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = nrfx_prs_acquire &rArr; app_util_critical_region_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prs_box_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
</UL>

<P><STRONG><a name="[1e1]"></a>nrfx_prs_release</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, nrfx_prs.o(i.nrfx_prs_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrfx_prs_release
</UL>
<BR>[Calls]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prs_box_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_uninit
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_uninit
</UL>

<P><STRONG><a name="[60]"></a>nrfx_uart_0_irq_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrfx_uart_0_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrfx_uart_0_irq_handler &rArr; uart_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrfx_uart.o(.constdata)
</UL>
<P><STRONG><a name="[1aa]"></a>nrfx_uart_init</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, nrfx_uart.o(i.nrfx_uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = nrfx_uart_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
</UL>

<P><STRONG><a name="[1ac]"></a>nrfx_uart_rx</STRONG> (Thumb, 230 bytes, Stack size 40 bytes, nrfx_uart.o(i.nrfx_uart_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uart_rx &rArr; rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_enable
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>

<P><STRONG><a name="[1ae]"></a>nrfx_uart_tx</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, nrfx_uart.o(i.nrfx_uart_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx_in_progress
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_tx
</UL>

<P><STRONG><a name="[b9]"></a>nrfx_uart_tx_in_progress</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrfx_uart_tx_in_progress))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
</UL>

<P><STRONG><a name="[1c6]"></a>nrfx_uart_uninit</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, nrfx_uart.o(i.nrfx_uart_uninit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = nrfx_uart_uninit &rArr; nrf_gpio_cfg_default &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_default
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_release
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_uart_panic_set
</UL>

<P><STRONG><a name="[61]"></a>nrfx_uarte_0_irq_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_uarte_0_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = nrfx_uarte_0_irq_handler &rArr; uarte_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrfx_uarte.o(.constdata)
</UL>
<P><STRONG><a name="[1a9]"></a>nrfx_uarte_init</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, nrfx_uarte.o(i.nrfx_uarte_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = nrfx_uarte_init &rArr; apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupts_enable
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
</UL>

<P><STRONG><a name="[1ab]"></a>nrfx_uarte_rx</STRONG> (Thumb, 256 bytes, Stack size 40 bytes, nrfx_uarte.o(i.nrfx_uarte_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = nrfx_uarte_rx &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_is_in_ram
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>

<P><STRONG><a name="[1ad]"></a>nrfx_uarte_tx</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, nrfx_uarte.o(i.nrfx_uarte_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrfx_uarte_tx &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx_in_progress
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_is_in_ram
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_tx
</UL>

<P><STRONG><a name="[b8]"></a>nrfx_uarte_tx_in_progress</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_uarte_tx_in_progress))
<BR><BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
</UL>

<P><STRONG><a name="[1c5]"></a>nrfx_uarte_uninit</STRONG> (Thumb, 254 bytes, Stack size 32 bytes, nrfx_uarte.o(i.nrfx_uarte_uninit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = nrfx_uarte_uninit &rArr; nrf_gpio_cfg_default &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_default
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_release
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_uart_panic_set
</UL>

<P><STRONG><a name="[93]"></a>nvmc_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, system_nrf52.o(i.nvmc_config))
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[59]"></a>pdb_pds_evt_handler</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, peer_database.o(i.pdb_pds_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = pdb_pds_evt_handler &rArr; reattempt_previous_operations &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_release
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reattempt_previous_operations
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_data_storage.o(.constdata)
</UL>
<P><STRONG><a name="[162]"></a>pdb_peer_data_ptr_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, peer_database.o(i.pdb_peer_data_ptr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_needed
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_apply
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
</UL>

<P><STRONG><a name="[172]"></a>pdb_peer_free</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, peer_database.o(i.pdb_peer_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_release
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find_next
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_free
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_free
</UL>

<P><STRONG><a name="[c6]"></a>pdb_write_buf_get</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, peer_database.o(i.pdb_write_buf_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_ptr_get
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_block_acquire
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_invalidate
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_point_to_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[167]"></a>pdb_write_buf_release</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, peer_database.o(i.pdb_write_buf_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = pdb_write_buf_release &rArr; write_buffer_record_release &rArr; pm_buffer_release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_release
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
</UL>

<P><STRONG><a name="[cf]"></a>pdb_write_buf_store</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, peer_database.o(i.pdb_write_buf_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = pdb_write_buf_store &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_is_allocated
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[1f9]"></a>pds_next_deleted_peer_id_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_next_deleted_peer_id_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pds_next_deleted_peer_id_get &rArr; peer_id_get_next_deleted &rArr; next_id_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_deleted
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
</UL>

<P><STRONG><a name="[1fb]"></a>pds_next_peer_id_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_next_peer_id_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = pds_next_peer_id_get &rArr; peer_id_get_next_used &rArr; next_id_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_used
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_peer_ranks_get
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
</UL>

<P><STRONG><a name="[16d]"></a>pds_peer_data_iterate</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, peer_data_storage.o(i.pds_peer_data_iterate))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = pds_peer_data_iterate &rArr; fds_record_open &rArr; record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_id_is_valid
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_open
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find_by_key
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_close
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_master_id
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_find_duplicate_bonding_data
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
</UL>

<P><STRONG><a name="[16c]"></a>pds_peer_data_iterate_prepare</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_peer_data_iterate_prepare))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_master_id
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_find_duplicate_bonding_data
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
</UL>

<P><STRONG><a name="[121]"></a>pds_peer_data_read</STRONG> (Thumb, 114 bytes, Stack size 48 bytes, peer_data_storage.o(i.pds_peer_data_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_id_is_valid
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_find
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_open
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_close
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_update_needed
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_peer_ranks_get
</UL>

<P><STRONG><a name="[164]"></a>pds_peer_data_store</STRONG> (Thumb, 168 bytes, Stack size 40 bytes, peer_data_storage.o(i.pds_peer_data_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_id_is_valid
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_find
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_write
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_update
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_id_from_desc
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_db_change_notification_done
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;store_car_value
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
</UL>

<P><STRONG><a name="[cd]"></a>pds_peer_id_allocate</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_peer_id_allocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = pds_peer_id_allocate &rArr; peer_id_allocate &rArr; claim &rArr; nrf_atflags_find_and_set_flag &rArr; nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_allocate
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[1f1]"></a>pds_peer_id_free</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, peer_data_storage.o(i.pds_peer_id_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_delete
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
</UL>

<P><STRONG><a name="[1f7]"></a>pds_peer_id_is_allocated</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_peer_id_is_allocated))
<BR><BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_is_allocated
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_store
</UL>

<P><STRONG><a name="[200]"></a>peer_id_allocate</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, peer_id.o(i.peer_id_allocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = peer_id_allocate &rArr; claim &rArr; nrf_atflags_find_and_set_flag &rArr; nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;claim
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_allocate
</UL>

<P><STRONG><a name="[201]"></a>peer_id_delete</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, peer_id.o(i.peer_id_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = peer_id_delete &rArr; claim &rArr; nrf_atflags_find_and_set_flag &rArr; nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;claim
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_free
</UL>

<P><STRONG><a name="[204]"></a>peer_id_free</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, peer_id.o(i.peer_id_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = peer_id_free &rArr; release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;release
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
</UL>

<P><STRONG><a name="[1fa]"></a>peer_id_get_next_deleted</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, peer_id.o(i.peer_id_get_next_deleted))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = peer_id_get_next_deleted &rArr; next_id_get
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;next_id_get
</UL>
<BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_deleted_peer_id_get
</UL>

<P><STRONG><a name="[1fc]"></a>peer_id_get_next_used</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, peer_id.o(i.peer_id_get_next_used))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = peer_id_get_next_used &rArr; next_id_get
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;next_id_get
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_is_deleted
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_peer_id_get
</UL>

<P><STRONG><a name="[203]"></a>peer_id_is_allocated</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, peer_id.o(i.peer_id_is_allocated))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_is_allocated
</UL>

<P><STRONG><a name="[207]"></a>peer_id_is_deleted</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, peer_id.o(i.peer_id_is_deleted))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_used
</UL>

<P><STRONG><a name="[1f3]"></a>pm_buffer_block_acquire</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, pm_buffer.o(i.pm_buffer_block_acquire))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = pm_buffer_block_acquire &rArr; pm_buffer_release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_fetch_set
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_release
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
</UL>

<P><STRONG><a name="[1f4]"></a>pm_buffer_ptr_get</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, pm_buffer.o(i.pm_buffer_ptr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pm_buffer_ptr_get
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mutex_lock_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
</UL>

<P><STRONG><a name="[208]"></a>pm_buffer_release</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, pm_buffer.o(i.pm_buffer_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = pm_buffer_release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_clear
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mutex_lock_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_block_acquire
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_release
</UL>

<P><STRONG><a name="[1]"></a>pm_gcm_evt_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_manager.o(i.pm_gcm_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = pm_gcm_evt_handler &rArr; evt_send
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatt_cache_manager.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>pm_gscm_evt_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_manager.o(i.pm_gscm_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = pm_gscm_evt_handler &rArr; evt_send
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatts_cache_manager.o(.data)
</UL>
<P><STRONG><a name="[57]"></a>pm_im_evt_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, peer_manager.o(i.pm_im_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = pm_im_evt_handler &rArr; evt_send
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> id_manager.o(.constdata)
</UL>
<P><STRONG><a name="[5a]"></a>pm_pdb_evt_handler</STRONG> (Thumb, 276 bytes, Stack size 48 bytes, peer_manager.o(i.pm_pdb_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = pm_pdb_evt_handler &rArr; rank_vars_update &rArr; pm_peer_ranks_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_peer_id_get
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_conn_handle_get
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rank_vars_update
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_deleted_peer_id_get
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_database.o(.constdata)
</UL>
<P><STRONG><a name="[20a]"></a>pm_peer_ranks_get</STRONG> (Thumb, 216 bytes, Stack size 72 bytes, peer_manager.o(i.pm_peer_ranks_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = pm_peer_ranks_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_peer_id_get
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rank_vars_update
</UL>

<P><STRONG><a name="[5f]"></a>pm_sm_evt_handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, peer_manager.o(i.pm_sm_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = pm_sm_evt_handler &rArr; evt_send
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> security_manager.o(.constdata)
</UL>
<P><STRONG><a name="[12e]"></a>services_init</STRONG> (Thumb, 282 bytes, Stack size 192 bytes, ble_conn.o(i.services_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = services_init &rArr; ble_dis_init &rArr; char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_qwr_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_ascii_to_utf8
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dis_init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_bas_init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[111]"></a>sm_ble_evt_handler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, security_manager.o(i.sm_ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = sm_ble_evt_handler &rArr; smd_ble_evt_handler &rArr; auth_status_success_process &rArr; im_peer_free &rArr; pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[21e]"></a>sm_conn_sec_status_get</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, security_manager.o(i.sm_conn_sec_status_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = sm_conn_sec_status_get &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_status
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_mitm_protected
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_lesc
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_encrypted
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_sec_is_sufficient
</UL>

<P><STRONG><a name="[5b]"></a>sm_pdb_evt_handler</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, security_manager.o(i.sm_pdb_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sm_pdb_evt_handler &rArr; ble_conn_state_for_each_set_user_flag &rArr; for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_database.o(.constdata)
</UL>
<P><STRONG><a name="[21a]"></a>sm_sec_is_sufficient</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, security_manager.o(i.sm_sec_is_sufficient))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = sm_sec_is_sufficient &rArr; sm_conn_sec_status_get &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_conn_sec_status_get
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_req_process
</UL>

<P><STRONG><a name="[5e]"></a>sm_smd_evt_handler</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, security_manager.o(i.sm_smd_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = sm_smd_evt_handler &rArr; smd_params_reply_perform &rArr; smd_params_reply &rArr; sec_keyset_fill &rArr; pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_req_process
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> security_dispatcher.o(.constdata)
</UL>
<P><STRONG><a name="[21d]"></a>smd_ble_evt_handler</STRONG> (Thumb, 234 bytes, Stack size 32 bytes, security_dispatcher.o(i.smd_ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = smd_ble_evt_handler &rArr; auth_status_success_process &rArr; im_peer_free &rArr; pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_role
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_encrypted
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_proc_start
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_failure
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encryption_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_ble_evt_handler
</UL>

<P><STRONG><a name="[17c]"></a>smd_link_secure</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, security_dispatcher.o(i.smd_link_secure))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = smd_link_secure &rArr; ble_conn_state_role
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_role
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>

<P><STRONG><a name="[21f]"></a>smd_params_reply</STRONG> (Thumb, 162 bytes, Stack size 56 bytes, security_dispatcher.o(i.smd_params_reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = smd_params_reply &rArr; sec_keyset_fill &rArr; pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_role
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_config_req
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_keyset_fill
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;allow_repairing
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
</UL>

<P><STRONG><a name="[15f]"></a>store_car_value</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, gatt_cache_manager.o(i.store_car_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = store_car_value &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
</UL>

<P><STRONG><a name="[47]"></a>uart_event_handle</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, ble_conn.o(i.uart_event_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = uart_event_handle &rArr; app_uart_get &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_get
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_data_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[1f8]"></a>write_buf_store</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, peer_database.o(i.write_buf_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_ptr_get
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_point_to_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_store
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reattempt_previous_operations
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9b]"></a>advertising_buttons_configure</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.advertising_buttons_configure))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[6a]"></a>ble_evt_handler</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, bsp_btn_ble.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ble_evt_handler &rArr; advertising_buttons_configure &rArr; bsp_event_to_button_action_assign
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;advertising_buttons_configure
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_event_to_button_action_assign
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_btn_ble.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[db]"></a>ble_device_addr_encode</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, ble_advdata.o(i.ble_device_addr_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ble_device_addr_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[de]"></a>conn_int_encode</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, ble_advdata.o(i.conn_int_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = conn_int_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uint16_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[df]"></a>manuf_specific_data_encode</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, ble_advdata.o(i.manuf_specific_data_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = manuf_specific_data_encode &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uint16_encode
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[e1]"></a>name_encode</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, ble_advdata.o(i.name_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = name_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[e0]"></a>service_data_encode</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, ble_advdata.o(i.service_data_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = service_data_encode &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uint16_encode
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[dc]"></a>uint16_encode</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ble_advdata.o(i.uint16_encode))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_data_encode
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;manuf_specific_data_encode
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_int_encode
</UL>

<P><STRONG><a name="[dd]"></a>uuid_list_encode</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, ble_advdata.o(i.uuid_list_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = uuid_list_encode &rArr; uuid_list_sized_encode
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uuid_list_sized_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_encode
</UL>

<P><STRONG><a name="[228]"></a>uuid_list_sized_encode</STRONG> (Thumb, 158 bytes, Stack size 48 bytes, ble_advdata.o(i.uuid_list_sized_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = uuid_list_sized_encode
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uuid_list_encode
</UL>

<P><STRONG><a name="[e5]"></a>adv_set_data_size_max_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ble_advertising.o(i.adv_set_data_size_max_get))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_init
</UL>

<P><STRONG><a name="[e9]"></a>flags_set</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ble_advertising.o(i.flags_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = flags_set &rArr; ble_advdata_parse &rArr; ble_advdata_search
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advdata_parse
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
</UL>

<P><STRONG><a name="[e7]"></a>phy_is_valid</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ble_advertising.o(i.phy_is_valid))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
</UL>

<P><STRONG><a name="[e8]"></a>use_whitelist</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ble_advertising.o(i.use_whitelist))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_start
</UL>

<P><STRONG><a name="[6b]"></a>ble_evt_handler</STRONG> (Thumb, 242 bytes, Stack size 32 bytes, ble_conn_params.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ble_evt_handler &rArr; conn_params_negotiation &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_is_notification_enabled
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_error_evt
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_conn_params_ok
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;instance_get
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_params_negotiation
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn_params.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[10b]"></a>conn_params_negotiation</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, ble_conn_params.o(i.conn_params_negotiation))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = conn_params_negotiation &rArr; app_timer_start &rArr; timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_error_evt
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[108]"></a>instance_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ble_conn_params.o(i.instance_get))
<BR><BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timeout_handler
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[109]"></a>is_conn_params_ok</STRONG> (Thumb, 68 bytes, Stack size 20 bytes, ble_conn_params.o(i.is_conn_params_ok))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = is_conn_params_ok
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[10a]"></a>send_error_evt</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ble_conn_params.o(i.send_error_evt))
<BR><BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_timeout_handler
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_params_negotiation
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
</UL>

<P><STRONG><a name="[3f]"></a>update_timeout_handler</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, ble_conn_params.o(i.update_timeout_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = update_timeout_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_error_evt
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;instance_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn_params.o(i.ble_conn_params_init)
</UL>
<P><STRONG><a name="[69]"></a>ble_evt_handler</STRONG> (Thumb, 274 bytes, Stack size 128 bytes, ble_conn_state.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = ble_evt_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flag_toggle
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_and
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_set
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_clear
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_handle_list_get
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn_state.o(sdh_ble_observers0)
</UL>
<P><STRONG><a name="[f9]"></a>flag_toggle</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ble_conn_state.o(i.flag_toggle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_set
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>

<P><STRONG><a name="[f2]"></a>for_each_set_flag</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, ble_conn_state.o(i.for_each_set_flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_connected
</UL>

<P><STRONG><a name="[f4]"></a>user_flag_is_acquired</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ble_conn_state.o(i.user_flag_is_acquired))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
</UL>

<P><STRONG><a name="[122]"></a>set_security_req</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ble_srv_common.o(i.set_security_req))
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptor_add
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
</UL>

<P><STRONG><a name="[46]"></a>apply_pending_handle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gatt_cache_manager.o(i.apply_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = apply_pending_handle &rArr; local_db_apply_in_evt &rArr; gscm_local_db_cache_apply &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatt_cache_manager.o(i.gcm_ble_evt_handler)
</UL>
<P><STRONG><a name="[120]"></a>car_update_needed</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, gatt_cache_manager.o(i.car_update_needed))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = car_update_needed &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_im_evt_handler
</UL>

<P><STRONG><a name="[56]"></a>car_update_pending_handle</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gatt_cache_manager.o(i.car_update_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = car_update_pending_handle &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatt_cache_manager.o(i.update_pending_flags_check)
</UL>
<P><STRONG><a name="[55]"></a>db_update_pending_handle</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, gatt_cache_manager.o(i.db_update_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = db_update_pending_handle &rArr; local_db_update_in_evt &rArr; gscm_local_db_cache_update &rArr; pdb_write_buf_store &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatt_cache_manager.o(i.update_pending_flags_check)
</UL>
<P><STRONG><a name="[13e]"></a>evt_send</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gatt_cache_manager.o(i.evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
</UL>

<P><STRONG><a name="[c3]"></a>local_db_apply_in_evt</STRONG> (Thumb, 148 bytes, Stack size 40 bytes, gatt_cache_manager.o(i.local_db_apply_in_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = local_db_apply_in_evt &rArr; gscm_local_db_cache_apply &rArr; pdb_peer_data_ptr_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_apply
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_im_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_pending_handle
</UL>

<P><STRONG><a name="[15d]"></a>local_db_update</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gatt_cache_manager.o(i.local_db_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = local_db_update &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
</UL>

<P><STRONG><a name="[133]"></a>local_db_update_in_evt</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, gatt_cache_manager.o(i.local_db_update_in_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = local_db_update_in_evt &rArr; gscm_local_db_cache_update &rArr; pdb_write_buf_store &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_local_db_cache_update
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;db_update_pending_handle
</UL>

<P><STRONG><a name="[4e]"></a>sc_send_pending_handle</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gatt_cache_manager.o(i.sc_send_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = sc_send_pending_handle &rArr; service_changed_send_in_evt &rArr; gscm_db_change_notification_done &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> gatt_cache_manager.o(i.service_changed_pending_flags_check)
</UL>
<P><STRONG><a name="[180]"></a>send_unexpected_error</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, gatt_cache_manager.o(i.send_unexpected_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = send_unexpected_error &rArr; evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_send_in_evt
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_update_in_evt
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
</UL>

<P><STRONG><a name="[160]"></a>service_changed_pending_flags_check</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gatt_cache_manager.o(i.service_changed_pending_flags_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = service_changed_pending_flags_check &rArr; ble_conn_state_for_each_set_user_flag &rArr; for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
</UL>

<P><STRONG><a name="[215]"></a>service_changed_send_in_evt</STRONG> (Thumb, 278 bytes, Stack size 88 bytes, gatt_cache_manager.o(i.service_changed_send_in_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 480<LI>Call Chain = service_changed_send_in_evt &rArr; gscm_db_change_notification_done &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_service_changed_ind_send
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_db_change_notification_done
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;local_db_apply_in_evt
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sc_send_pending_handle
</UL>

<P><STRONG><a name="[15e]"></a>update_pending_flags_check</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, gatt_cache_manager.o(i.update_pending_flags_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = update_pending_flags_check &rArr; ble_conn_state_for_each_set_user_flag &rArr; for_each_set_flag
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_set_user_flag
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_pdb_evt_handler
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_im_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
</UL>

<P><STRONG><a name="[13f]"></a>evt_send</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gatts_cache_manager.o(i.evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = evt_send &rArr; im_conn_handle_get
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_conn_handle_get
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;service_changed_pending_set
</UL>

<P><STRONG><a name="[168]"></a>service_changed_pending_set</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, gatts_cache_manager.o(i.service_changed_pending_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = service_changed_pending_set &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_next_peer_id_get
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gscm_pdb_evt_handler
</UL>

<P><STRONG><a name="[131]"></a>data_length_update</STRONG> (Thumb, 110 bytes, Stack size 48 bytes, nrf_ble_gatt.o(i.data_length_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;on_connected_evt
</UL>

<P><STRONG><a name="[19e]"></a>link_init</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nrf_ble_gatt.o(i.link_init))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_init
</UL>

<P><STRONG><a name="[19f]"></a>on_connected_evt</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, nrf_ble_gatt.o(i.on_connected_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = on_connected_evt &rArr; data_length_update &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_length_update
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[1a0]"></a>on_exchange_mtu_request_evt</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, nrf_ble_gatt.o(i.on_exchange_mtu_request_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = on_exchange_mtu_request_evt &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_gatt_on_ble_evt
</UL>

<P><STRONG><a name="[1a1]"></a>user_mem_reply</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nrf_ble_qwr.o(i.user_mem_reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = user_mem_reply
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_qwr_on_ble_evt
</UL>

<P><STRONG><a name="[205]"></a>pds_evt_send</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, peer_data_storage.o(i.pds_evt_send))
<BR><BR>[Called By]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_delete_process
</UL>

<P><STRONG><a name="[202]"></a>peer_data_delete_process</STRONG> (Thumb, 146 bytes, Stack size 72 bytes, peer_data_storage.o(i.peer_data_delete_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_evt_send
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_get_next_deleted
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_free
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find_in_file
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_file_delete
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_free
</UL>

<P><STRONG><a name="[1fe]"></a>peer_data_find</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, peer_data_storage.o(i.peer_data_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_id_is_valid
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
</UL>

<P><STRONG><a name="[1fd]"></a>peer_data_id_is_valid</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, peer_data_storage.o(i.peer_data_id_is_valid))
<BR><BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_data_find
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_iterate
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_store
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_data_read
</UL>

<P><STRONG><a name="[1ee]"></a>pdb_evt_send</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, peer_database.o(i.pdb_evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pdb_evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reattempt_previous_operations
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_pds_evt_handler
</UL>

<P><STRONG><a name="[1f5]"></a>peer_data_point_to_buffer</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, peer_database.o(i.peer_data_point_to_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = peer_data_point_to_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
</UL>

<P><STRONG><a name="[1ef]"></a>reattempt_previous_operations</STRONG> (Thumb, 148 bytes, Stack size 48 bytes, peer_database.o(i.reattempt_previous_operations))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = reattempt_previous_operations &rArr; write_buf_store &rArr; pds_peer_data_store &rArr; fds_record_write &rArr; write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buf_store
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_pds_evt_handler
</UL>

<P><STRONG><a name="[1f2]"></a>write_buffer_record_find</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, peer_database.o(i.write_buffer_record_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = write_buffer_record_find &rArr; write_buffer_record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find_next
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_store
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_release
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
</UL>

<P><STRONG><a name="[1f0]"></a>write_buffer_record_find_next</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, peer_database.o(i.write_buffer_record_find_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = write_buffer_record_find_next
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_free
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_find
</UL>

<P><STRONG><a name="[1f6]"></a>write_buffer_record_invalidate</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, peer_database.o(i.write_buffer_record_invalidate))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_release
</UL>

<P><STRONG><a name="[1ed]"></a>write_buffer_record_release</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, peer_database.o(i.write_buffer_record_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = write_buffer_record_release &rArr; pm_buffer_release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_release
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_buffer_record_invalidate
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_release
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_pds_evt_handler
</UL>

<P><STRONG><a name="[123]"></a>claim</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, peer_id.o(i.claim))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = claim &rArr; nrf_atflags_find_and_set_flag &rArr; nrf_atflags_fetch_set &rArr; nrf_atomic_u32_fetch_or
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_find_and_set_flag
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_fetch_set
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_delete
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_allocate
</UL>

<P><STRONG><a name="[206]"></a>release</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, peer_id.o(i.release))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = release &rArr; nrf_atflags_clear &rArr; nrf_atomic_u32_and
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peer_id_free
</UL>

<P><STRONG><a name="[6c]"></a>ble_evt_handler</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, peer_manager.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = ble_evt_handler &rArr; sm_ble_evt_handler &rArr; smd_ble_evt_handler &rArr; auth_status_success_process &rArr; im_peer_free &rArr; pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_evt_handler
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gcm_ble_evt_handler
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_ble_evt_handler
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> peer_manager.o(sdh_ble_observers1)
</UL>
<P><STRONG><a name="[113]"></a>evt_send</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, peer_manager.o(i.evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_im_evt_handler
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_gscm_evt_handler
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_gcm_evt_handler
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_sm_evt_handler
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_evt_handler
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
</UL>

<P><STRONG><a name="[209]"></a>rank_vars_update</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, peer_manager.o(i.rank_vars_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = rank_vars_update &rArr; pm_peer_ranks_get &rArr; pds_peer_data_read &rArr; peer_data_find &rArr; fds_record_find &rArr; record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_peer_ranks_get
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_pdb_evt_handler
</UL>

<P><STRONG><a name="[18a]"></a>mutex_lock_status_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pm_buffer.o(i.mutex_lock_status_get))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atflags_get
</UL>
<BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_release
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm_buffer_ptr_get
</UL>

<P><STRONG><a name="[a2]"></a>allow_repairing</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, security_dispatcher.o(i.allow_repairing))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = allow_repairing &rArr; ble_conn_state_user_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
</UL>

<P><STRONG><a name="[c4]"></a>auth_status_success_process</STRONG> (Thumb, 306 bytes, Stack size 72 bytes, security_dispatcher.o(i.auth_status_success_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = auth_status_success_process &rArr; im_peer_free &rArr; pdb_peer_free &rArr; pds_peer_id_free &rArr; peer_data_delete_process &rArr; fds_file_delete &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_free
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_new_peer_id
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_find_duplicate_bonding_data
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_store
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_config_req
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_success_evt_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;allow_repairing
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pds_peer_id_allocate
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[130]"></a>conn_sec_failure</STRONG> (Thumb, 60 bytes, Stack size 40 bytes, security_dispatcher.o(i.conn_sec_failure))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = conn_sec_failure &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encryption_failure
</UL>

<P><STRONG><a name="[139]"></a>encryption_failure</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, security_dispatcher.o(i.encryption_failure))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = encryption_failure &rArr; conn_sec_failure &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_sec_failure
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_failure
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[d3]"></a>evt_send</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, security_dispatcher.o(i.evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_config_req
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_proc_start
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_success_evt_send
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_sec_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[17d]"></a>link_secure_failure</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, security_dispatcher.o(i.link_secure_failure))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = link_secure_failure &rArr; pairing_failure &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encryption_failure
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[17e]"></a>pairing</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, security_dispatcher.o(i.pairing))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pairing &rArr; ble_conn_state_user_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_failure
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[17f]"></a>pairing_failure</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, security_dispatcher.o(i.pairing_failure))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = pairing_failure &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_release
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conn_sec_failure
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_failure
</UL>

<P><STRONG><a name="[d1]"></a>pairing_success_evt_send</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, security_dispatcher.o(i.pairing_success_evt_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pairing_success_evt_send &rArr; evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[216]"></a>sec_info_request_process</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, security_dispatcher.o(i.sec_info_request_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = sec_info_request_process &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_master_id
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_new_peer_id
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_master_ids_compare
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_peer_data_ptr_get
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_unexpected_error
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_proc_start
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encryption_failure
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
</UL>
<BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[218]"></a>sec_keyset_fill</STRONG> (Thumb, 168 bytes, Stack size 40 bytes, security_dispatcher.o(i.sec_keyset_fill))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = sec_keyset_fill &rArr; pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_ble_addr_get
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pdb_write_buf_get
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
</UL>

<P><STRONG><a name="[217]"></a>sec_proc_start</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, security_dispatcher.o(i.sec_proc_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sec_proc_start &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_ble_evt_handler
</UL>

<P><STRONG><a name="[cc]"></a>send_config_req</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, security_dispatcher.o(i.send_config_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = send_config_req &rArr; evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
</UL>

<P><STRONG><a name="[ce]"></a>send_unexpected_error</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, security_dispatcher.o(i.send_unexpected_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = send_unexpected_error &rArr; evt_send &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_info_request_process
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pairing_failure
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;auth_status_success_process
</UL>

<P><STRONG><a name="[13b]"></a>events_send_from_err_code</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, security_manager.o(i.events_send_from_err_code))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = events_send_from_err_code &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_evt
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>

<P><STRONG><a name="[13d]"></a>evt_send</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, security_manager.o(i.evt_send))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;params_req_send
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_smd_evt_handler
</UL>

<P><STRONG><a name="[14d]"></a>flags_set_from_err_code</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, security_manager.o(i.flags_set_from_err_code))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = flags_set_from_err_code &rArr; ble_conn_state_user_flag_set &rArr; flag_toggle &rArr; nrf_atflags_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>

<P><STRONG><a name="[179]"></a>link_secure</STRONG> (Thumb, 190 bytes, Stack size 48 bytes, security_manager.o(i.link_secure))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = link_secure &rArr; events_send_from_err_code &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_strerror_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_set
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;params_req_send
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_context_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flags_set_from_err_code
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_link_secure
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sec_req_process
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure_pending_handle
</UL>

<P><STRONG><a name="[53]"></a>link_secure_pending_handle</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, security_manager.o(i.link_secure_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = link_secure_pending_handle &rArr; link_secure &rArr; events_send_from_err_code &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_user_flag_get
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>
<BR>[Address Reference Count : 2]<UL><LI> security_manager.o(i.sm_ble_evt_handler)
<LI> security_manager.o(i.sm_pdb_evt_handler)
</UL>
<P><STRONG><a name="[17a]"></a>new_context_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, security_manager.o(i.new_context_get))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>

<P><STRONG><a name="[13c]"></a>new_evt</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, security_manager.o(i.new_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = new_evt &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;im_peer_id_get_by_conn_handle
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;params_req_send
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
</UL>

<P><STRONG><a name="[52]"></a>params_reply_pending_handle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, security_manager.o(i.params_reply_pending_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = params_reply_pending_handle &rArr; smd_params_reply_perform &rArr; smd_params_reply &rArr; sec_keyset_fill &rArr; pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
</UL>
<BR>[Address Reference Count : 2]<UL><LI> security_manager.o(i.sm_ble_evt_handler)
<LI> security_manager.o(i.sm_pdb_evt_handler)
</UL>
<P><STRONG><a name="[17b]"></a>params_req_send</STRONG> (Thumb, 32 bytes, Stack size 32 bytes, security_manager.o(i.params_req_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = params_req_send &rArr; new_evt &rArr; im_peer_id_get_by_conn_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_evt
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;evt_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply_perform
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>

<P><STRONG><a name="[219]"></a>sec_req_process</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, security_manager.o(i.sec_req_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = sec_req_process &rArr; link_secure &rArr; events_send_from_err_code &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_encrypted
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_sec_is_sufficient
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;link_secure
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_smd_evt_handler
</UL>

<P><STRONG><a name="[1ec]"></a>smd_params_reply_perform</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, security_manager.o(i.smd_params_reply_perform))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = smd_params_reply_perform &rArr; smd_params_reply &rArr; sec_keyset_fill &rArr; pdb_write_buf_get &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;params_req_send
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;new_context_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flags_set_from_err_code
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;events_send_from_err_code
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smd_params_reply
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;params_reply_pending_handle
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sm_smd_evt_handler
</UL>

<P><STRONG><a name="[126]"></a>clock_clk_started_notify</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.clock_clk_started_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_clk_started_notify
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soc_evt_handler
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_irq_handler
</UL>

<P><STRONG><a name="[48]"></a>clock_irq_handler</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.clock_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = clock_irq_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(i.nrf_drv_clock_init)
</UL>
<P><STRONG><a name="[1a6]"></a>nrf_wdt_started</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.nrf_wdt_started))
<BR><BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>

<P><STRONG><a name="[7a]"></a>sd_state_evt_handler</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, nrf_drv_clock.o(i.sd_state_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_state_evt_handler &rArr; nrf_drv_clock_lfclk_release &rArr; nrfx_clock_lfclk_stop &rArr; nrf_clock_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_enable
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_lfclk_release
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_clock_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_state_observers0)
</UL>
<P><STRONG><a name="[75]"></a>soc_evt_handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, nrf_drv_clock.o(i.soc_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = soc_evt_handler &rArr; clock_clk_started_notify
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_clk_started_notify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_clock.o(sdh_soc_observers0)
</UL>
<P><STRONG><a name="[4a]"></a>uart_evt_handler</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, nrf_drv_uart.o(i.uart_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart_evt_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_uart.o(i.nrf_drv_uart_init)
</UL>
<P><STRONG><a name="[49]"></a>uarte_evt_handler</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, nrf_drv_uart.o(i.uarte_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uarte_evt_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_drv_uart.o(i.nrf_drv_uart_init)
</UL>
<P><STRONG><a name="[89]"></a>nrf_clock_event_check</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_clock.o(i.nrf_clock_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
</UL>

<P><STRONG><a name="[8a]"></a>nrf_clock_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_clock.o(i.nrf_clock_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_clock_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;POWER_CLOCK_IRQHandler
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_clock_lfclk_stop
</UL>

<P><STRONG><a name="[20d]"></a>channel_port_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.channel_port_get))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[20b]"></a>nrf_bitmask_bit_is_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_bitmask_bit_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[1bc]"></a>nrf_gpio_cfg_sense_set</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, nrfx_gpiote.o(i.nrf_gpio_cfg_sense_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nrf_gpio_cfg_sense_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[87]"></a>nrf_gpio_latches_read_and_clear</STRONG> (Thumb, 42 bytes, Stack size 20 bytes, nrfx_gpiote.o(i.nrf_gpio_latches_read_and_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = nrf_gpio_latches_read_and_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[1bd]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
</UL>

<P><STRONG><a name="[86]"></a>nrf_gpiote_event_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpiote_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>nrf_gpiote_event_is_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.nrf_gpiote_event_is_set))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[88]"></a>port_event_handle</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, nrfx_gpiote.o(i.port_event_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = port_event_handle &rArr; nrf_gpio_latches_read_and_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_handler_polarity_get
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_latches_read_and_clear
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_sense_set
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_bitmask_bit_is_set
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;channel_port_get
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOTE_IRQHandler
</UL>

<P><STRONG><a name="[20c]"></a>port_handler_polarity_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_gpiote.o(i.port_handler_polarity_get))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;port_event_handle
</UL>

<P><STRONG><a name="[1e0]"></a>prs_box_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, nrfx_prs.o(i.prs_box_get))
<BR><BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_release
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_prs_acquire
</UL>

<P><STRONG><a name="[bb]"></a>apply_config</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, nrfx_uart.o(i.apply_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
</UL>

<P><STRONG><a name="[1b6]"></a>nrf_gpio_cfg</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, nrfx_uart.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_default
</UL>

<P><STRONG><a name="[1ba]"></a>nrf_gpio_cfg_default</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_default &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_uninit
</UL>

<P><STRONG><a name="[be]"></a>nrf_gpio_cfg_input</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_input &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[bd]"></a>nrf_gpio_cfg_output</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, nrfx_uart.o(i.nrf_gpio_cfg_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[1b7]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>

<P><STRONG><a name="[bc]"></a>nrf_gpio_pin_set</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nrfx_uart.o(i.nrf_gpio_pin_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpio_pin_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[1e5]"></a>nrf_uart_event_check</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrf_uart_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[1e3]"></a>nrf_uart_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrfx_uart.o(i.nrf_uart_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_init
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_enable
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
</UL>

<P><STRONG><a name="[223]"></a>nrf_uart_int_enable_check</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrfx_uart.o(i.nrf_uart_int_enable_check))
<BR><BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[1e6]"></a>rx_byte</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, nrfx_uart.o(i.rx_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[224]"></a>rx_done_event</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, nrfx_uart.o(i.rx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[1e4]"></a>rx_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, nrfx_uart.o(i.rx_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rx_enable &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>

<P><STRONG><a name="[1e7]"></a>tx_byte</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, nrfx_uart.o(i.tx_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[225]"></a>tx_done_event</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrfx_uart.o(i.tx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_irq_handler
</UL>

<P><STRONG><a name="[1e2]"></a>uart_irq_handler</STRONG> (Thumb, 298 bytes, Stack size 48 bytes, nrfx_uart.o(i.uart_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = uart_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_done_event
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_byte
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_done_event
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_byte
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_int_enable_check
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uart_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_0_irq_handler
</UL>

<P><STRONG><a name="[bf]"></a>apply_config</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, nrfx_uarte.o(i.apply_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = apply_config &rArr; nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
</UL>

<P><STRONG><a name="[177]"></a>interrupts_enable</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, nrfx_uarte.o(i.interrupts_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = interrupts_enable &rArr; nrf_uarte_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_init
</UL>

<P><STRONG><a name="[1b8]"></a>nrf_gpio_cfg</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, nrfx_uarte.o(i.nrf_gpio_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_output
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_input
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg_default
</UL>

<P><STRONG><a name="[1bb]"></a>nrf_gpio_cfg_default</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_default &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_uninit
</UL>

<P><STRONG><a name="[c2]"></a>nrf_gpio_cfg_input</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_input &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[c1]"></a>nrf_gpio_cfg_output</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, nrfx_uarte.o(i.nrf_gpio_cfg_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = nrf_gpio_cfg_output &rArr; nrf_gpio_cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[1b9]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_cfg
</UL>

<P><STRONG><a name="[c0]"></a>nrf_gpio_pin_set</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nrfx_uarte.o(i.nrf_gpio_pin_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpio_pin_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;apply_config
</UL>

<P><STRONG><a name="[1ea]"></a>nrf_uarte_event_check</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrf_uarte_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_uninit
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[178]"></a>nrf_uarte_event_clear</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, nrfx_uarte.o(i.nrf_uarte_event_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_uarte_event_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_uninit
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interrupts_enable
</UL>

<P><STRONG><a name="[1e9]"></a>nrfx_is_in_ram</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, nrfx_uarte.o(i.nrfx_is_in_ram))
<BR><BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
</UL>

<P><STRONG><a name="[226]"></a>rx_done_event</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, nrfx_uarte.o(i.rx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[227]"></a>tx_done_event</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, nrfx_uarte.o(i.tx_done_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tx_done_event
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uarte_irq_handler
</UL>

<P><STRONG><a name="[1e8]"></a>uarte_irq_handler</STRONG> (Thumb, 274 bytes, Stack size 40 bytes, nrfx_uarte.o(i.uarte_irq_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = uarte_irq_handler &rArr; tx_done_event
</UL>
<BR>[Calls]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tx_done_event
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rx_done_event
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_clear
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_uarte_event_check
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_0_irq_handler
</UL>

<P><STRONG><a name="[90]"></a>nrf_gpio_pin_clear</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nrfx_spim.o(i.nrf_gpio_pin_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpio_pin_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
</UL>

<P><STRONG><a name="[1be]"></a>nrf_gpio_pin_port_decode</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nrfx_spim.o(i.nrf_gpio_pin_port_decode))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_clear
</UL>

<P><STRONG><a name="[91]"></a>nrf_gpio_pin_set</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nrfx_spim.o(i.nrf_gpio_pin_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_gpio_pin_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_gpio_pin_port_decode
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>nrf_spim_event_check</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrfx_spim.o(i.nrf_spim_event_check))
<BR><BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
</UL>

<P><STRONG><a name="[62]"></a>compare_func</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, app_timer2.o(i.compare_func))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = compare_func
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_timer2.o(.constdata)
</UL>
<P><STRONG><a name="[ae]"></a>get_now</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, app_timer2.o(i.get_now))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_now
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_counter_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>

<P><STRONG><a name="[af]"></a>timer_req_schedule</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, app_timer2.o(i.timer_req_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = timer_req_schedule &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drv_rtc_irq_trigger
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_stop
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_timer_start
</UL>

<P><STRONG><a name="[134]"></a>delete_execute</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, fds.o(i.delete_execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = delete_execute &rArr; record_header_flag_dirty &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_header_flag_dirty
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[210]"></a>event_prepare</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, fds.o(i.event_prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = event_prepare
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[211]"></a>event_send</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, fds.o(i.event_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = event_send
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[3]"></a>fs_event_handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fds.o(i.fs_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = fs_event_handler &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> fds.o(fs_data)
</UL>
<P><STRONG><a name="[150]"></a>gc_execute</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fds.o(i.gc_execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_swap
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_data
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_state_advance
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_find_next
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_copy
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_page_erase
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_next_page
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[152]"></a>gc_next_page</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, fds.o(i.gc_next_page))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_find_next
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[155]"></a>gc_page_erase</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, fds.o(i.gc_page_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = gc_page_erase &rArr; nrf_fstorage_erase &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_find_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[154]"></a>gc_record_copy</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, fds.o(i.gc_record_copy))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_find_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[153]"></a>gc_record_find_next</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, fds.o(i.gc_record_find_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_next
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_copy
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_page_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_next_page
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[151]"></a>gc_state_advance</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fds.o(i.gc_state_advance))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = gc_state_advance
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_swap_pages
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[15b]"></a>gc_swap_pages</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fds.o(i.gc_swap_pages))
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_state_advance
</UL>

<P><STRONG><a name="[213]"></a>header_check</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, fds.o(i.header_check))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_next
</UL>

<P><STRONG><a name="[214]"></a>header_has_next</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fds.o(i.header_has_next))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_next
</UL>

<P><STRONG><a name="[174]"></a>init_execute</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, fds.o(i.init_execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = init_execute &rArr; page_tag_write_swap &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_erase
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_swap
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_tag_write_data
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[212]"></a>is_word_aligned</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, fds.o(i.is_word_aligned))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
</UL>

<P><STRONG><a name="[22b]"></a>page_offsets_update</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fds.o(i.page_offsets_update))
<BR><BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_execute
</UL>

<P><STRONG><a name="[156]"></a>page_tag_write_data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fds.o(i.page_tag_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = page_tag_write_data &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_execute
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[157]"></a>page_tag_write_swap</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fds.o(i.page_tag_write_swap))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = page_tag_write_swap &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_execute
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
</UL>

<P><STRONG><a name="[142]"></a>queue_buf_get</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fds.o(i.queue_buf_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = queue_buf_get &rArr; nrf_atfifo_item_alloc
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_alloc
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_file_delete
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>

<P><STRONG><a name="[143]"></a>queue_buf_store</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fds.o(i.queue_buf_store))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = queue_buf_store &rArr; nrf_atfifo_item_put
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_put
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_file_delete
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>

<P><STRONG><a name="[14e]"></a>queue_process</STRONG> (Thumb, 154 bytes, Stack size 40 bytes, fds.o(i.queue_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_sub
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_execute
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_execute
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_execute
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_prepare
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delete_execute
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_start
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fs_event_handler
</UL>

<P><STRONG><a name="[144]"></a>queue_start</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fds.o(i.queue_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_add
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_file_delete
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>

<P><STRONG><a name="[137]"></a>record_find</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, fds.o(i.record_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = record_find &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_next
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find_in_file
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find_by_key
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_find
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delete_execute
</UL>

<P><STRONG><a name="[135]"></a>record_find_by_desc</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, fds.o(i.record_find_by_desc))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = record_find_by_desc &rArr; record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_next
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_word_aligned
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_open
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_close
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_execute
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delete_execute
</UL>

<P><STRONG><a name="[15a]"></a>record_find_next</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, fds.o(i.record_find_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = record_find_next
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;header_has_next
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;header_check
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gc_record_find_next
</UL>

<P><STRONG><a name="[136]"></a>record_header_flag_dirty</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, fds.o(i.record_header_flag_dirty))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = record_header_flag_dirty &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
</UL>
<BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_execute
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delete_execute
</UL>

<P><STRONG><a name="[14b]"></a>write_enqueue</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, fds.o(i.write_enqueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = write_enqueue &rArr; queue_start &rArr; queue_process &rArr; gc_execute &rArr; gc_next_page &rArr; gc_record_find_next &rArr; gc_record_copy &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_space_reserve
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_space_free
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_start
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_store
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_buf_get
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_word_aligned
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_write
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fds_record_update
</UL>

<P><STRONG><a name="[20f]"></a>write_execute</STRONG> (Thumb, 236 bytes, Stack size 32 bytes, fds.o(i.write_execute))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = write_execute &rArr; record_header_flag_dirty &rArr; nrf_fstorage_write &rArr; nrf_log_frontend_std_3 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_header_flag_dirty
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;record_find_by_desc
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;page_offsets_update
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[22a]"></a>write_space_free</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, fds.o(i.write_space_free))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>

<P><STRONG><a name="[229]"></a>write_space_reserve</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, fds.o(i.write_space_reserve))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = write_space_reserve
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_enqueue
</UL>

<P><STRONG><a name="[11e]"></a>buffer_add</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, nrf_fprintf_format.o(i.buffer_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + In Cycle
<LI>Call Chain = buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[175]"></a>int_print</STRONG> (Thumb, 166 bytes, Stack size 48 bytes, nrf_fprintf_format.o(i.int_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;unsigned_print
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[176]"></a>unsigned_print</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, nrf_fprintf_format.o(i.unsigned_print))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buffer_add
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_print
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_fmt
</UL>

<P><STRONG><a name="[1b5]"></a>addr_is_aligned32</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, nrf_fstorage.o(i.addr_is_aligned32))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
</UL>

<P><STRONG><a name="[1b1]"></a>addr_is_within_bounds</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, nrf_fstorage.o(i.addr_is_within_bounds))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_write
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_erase
</UL>

<P><STRONG><a name="[13a]"></a>event_send</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, nrf_fstorage_sd.o(i.event_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = event_send
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sys_evt_handler
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[1b3]"></a>queue_free</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nrf_fstorage_sd.o(i.queue_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = queue_free &rArr; nrf_atfifo_item_free
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_free
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sys_evt_handler
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_process
</UL>

<P><STRONG><a name="[1b2]"></a>queue_process</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, nrf_fstorage_sd.o(i.queue_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = queue_process &rArr; nrf_fstorage_sys_evt_handler &rArr;  queue_process (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atfifo_item_get
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sys_evt_handler
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;queue_free
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;event_send
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sys_evt_handler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fstorage_sdh_state_handler
</UL>

<P><STRONG><a name="[187]"></a>memobj_op</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, nrf_memobj.o(i.memobj_op))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = memobj_op &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_write
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_memobj_read
</UL>

<P><STRONG><a name="[1d4]"></a>shutdown_process</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, nrf_pwr_mgmt.o(i.shutdown_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_panic
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_is_enabled
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
</UL>

<P><STRONG><a name="[1dd]"></a>nrf_section_iter_item_set</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nrf_section_iter.o(i.nrf_section_iter_item_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = nrf_section_iter_item_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
</UL>

<P><STRONG><a name="[b2]"></a>nrf_drv_uart_rx</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_uart_fifo.o(i.nrf_drv_uart_rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrf_drv_uart_rx &rArr; nrfx_uart_rx &rArr; rx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_rx
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_rx
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_get
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[ba]"></a>nrf_drv_uart_tx</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, app_uart_fifo.o(i.nrf_drv_uart_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_uart_put
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_event_handler
</UL>

<P><STRONG><a name="[3e]"></a>uart_event_handler</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, app_uart_fifo.o(i.uart_event_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = uart_event_handler &rArr; nrf_drv_uart_tx &rArr; nrfx_uart_tx &rArr; tx_byte &rArr; nrf_uart_event_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_tx
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_rx
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_uart_fifo.o(i.app_uart_init)
</UL>
<P><STRONG><a name="[a8]"></a>fifo_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, app_fifo.o(i.fifo_get))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_get
</UL>

<P><STRONG><a name="[aa]"></a>fifo_put</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, app_fifo.o(i.fifo_put))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_fifo_put
</UL>

<P><STRONG><a name="[65]"></a>nrf_log_backend_rtt_flush</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_flush))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[64]"></a>nrf_log_backend_rtt_panic_set</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_panic_set))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[63]"></a>nrf_log_backend_rtt_put</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = nrf_log_backend_rtt_put &rArr; nrf_log_backend_serial_put &rArr; nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(.constdata)
</UL>
<P><STRONG><a name="[4b]"></a>serial_tx</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, nrf_log_backend_rtt.o(i.serial_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = serial_tx &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_rtt.o(i.nrf_log_backend_rtt_put)
</UL>
<P><STRONG><a name="[68]"></a>nrf_log_backend_uart_flush</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nrf_log_backend_uart.o(i.nrf_log_backend_uart_flush))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_uart.o(.constdata)
</UL>
<P><STRONG><a name="[67]"></a>nrf_log_backend_uart_panic_set</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, nrf_log_backend_uart.o(i.nrf_log_backend_uart_panic_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = nrf_log_backend_uart_panic_set &rArr; uart_init &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_uninit
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_uninit
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_uart.o(.constdata)
</UL>
<P><STRONG><a name="[66]"></a>nrf_log_backend_uart_put</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrf_log_backend_uart.o(i.nrf_log_backend_uart_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = nrf_log_backend_uart_put &rArr; nrf_log_backend_serial_put &rArr; nrf_log_std_entry_process &rArr; prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_serial_put
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_uart.o(.constdata)
</UL>
<P><STRONG><a name="[4c]"></a>serial_tx</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, nrf_log_backend_uart.o(i.serial_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = serial_tx &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uarte_tx
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrfx_uart_tx
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_uart.o(i.nrf_log_backend_uart_put)
</UL>
<P><STRONG><a name="[54]"></a>uart_evt_handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, nrf_log_backend_uart.o(i.uart_evt_handler))
<BR>[Address Reference Count : 1]<UL><LI> nrf_log_backend_uart.o(i.uart_init)
</UL>
<P><STRONG><a name="[1c7]"></a>uart_init</STRONG> (Thumb, 66 bytes, Stack size 40 bytes, nrf_log_backend_uart.o(i.uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = uart_init &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_drv_uart_init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_backend_uart_panic_set
</UL>

<P><STRONG><a name="[11b]"></a>buf_prealloc</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.buf_prealloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = buf_prealloc &rArr; log_skip &rArr; nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_exit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_util_critical_region_enter
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_add
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[138]"></a>dropped_sat16_get</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, nrf_log_frontend.o(i.dropped_sat16_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dropped_sat16_get &rArr; nrf_atomic_u32_fetch_store
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_u32_fetch_store
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std_n
</UL>

<P><STRONG><a name="[182]"></a>invalid_packets_omit</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, nrf_log_frontend.o(i.invalid_packets_omit))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_skip
</UL>

<P><STRONG><a name="[11d]"></a>log_skip</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.log_skip))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = log_skip &rArr; nrf_atomic_flag_set &rArr; nrf_atomic_u32_or
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_set
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_atomic_flag_clear_fetch
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invalid_packets_omit
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>

<P><STRONG><a name="[188]"></a>module_idx_get</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, nrf_log_frontend.o(i.module_idx_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = module_idx_get
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_cnt_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_name_get
</UL>

<P><STRONG><a name="[1cb]"></a>std_n</STRONG> (Thumb, 144 bytes, Stack size 40 bytes, nrf_log_frontend.o(i.std_n))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + In Cycle
<LI>Call Chain = std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_dequeue
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_pending_hook
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dropped_sat16_get
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buf_prealloc
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_3
</UL>

<P><STRONG><a name="[1ce]"></a>postfix_process</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, nrf_log_str_formatter.o(i.postfix_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = postfix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf_buffer_flush
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[1cc]"></a>prefix_process</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, nrf_log_str_formatter.o(i.prefix_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = prefix_process &rArr; nrf_fprintf &rArr; nrf_fprintf_fmt &rArr; int_print &rArr; unsigned_print &rArr; buffer_add &rArr;  buffer_add (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_fprintf
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_module_name_get
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_color_id_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_std_entry_process
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_hexdump_entry_process
</UL>

<P><STRONG><a name="[8c]"></a>_GetAvailWriteSpace</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, segger_rtt.o(i._GetAvailWriteSpace))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[8e]"></a>_WriteBlocking</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, segger_rtt.o(i._WriteBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[8d]"></a>_WriteNoCheck</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, segger_rtt.o(i._WriteNoCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WriteNoCheck &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[220]"></a>__sd_nvic_app_accessible_irq</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nrf_sdh.o(i.__sd_nvic_app_accessible_irq))
<BR><BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevices_evt_irq_enable
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;softdevice_evt_irq_disable
</UL>

<P><STRONG><a name="[1d9]"></a>sdh_request_observer_notify</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_request_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_request_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>

<P><STRONG><a name="[1da]"></a>sdh_state_observer_notify</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, nrf_sdh.o(i.sdh_state_observer_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sdh_state_observer_notify &rArr; nrf_section_iter_next &rArr; nrf_section_iter_item_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>

<P><STRONG><a name="[1db]"></a>softdevice_evt_irq_disable</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, nrf_sdh.o(i.softdevice_evt_irq_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = softdevice_evt_irq_disable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sd_nvic_app_accessible_irq
</UL>
<BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_disable_request
</UL>

<P><STRONG><a name="[1dc]"></a>softdevices_evt_irq_enable</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, nrf_sdh.o(i.softdevices_evt_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = softdevices_evt_irq_enable &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sd_nvic_app_accessible_irq
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_sdh_enable_request
</UL>

<P><STRONG><a name="[78]"></a>nrf_sdh_ble_evts_poll</STRONG> (Thumb, 82 bytes, Stack size 80 bytes, nrf_sdh_ble.o(i.nrf_sdh_ble_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = nrf_sdh_ble_evts_poll &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_ble.o(sdh_stack_observers0)
</UL>
<P><STRONG><a name="[79]"></a>nrf_sdh_soc_evts_poll</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, nrf_sdh_soc.o(i.nrf_sdh_soc_evts_poll))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = nrf_sdh_soc_evts_poll &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_next
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_section_iter_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> nrf_sdh_soc.o(sdh_stack_observers0)
</UL>
<P><STRONG><a name="[51]"></a>ble_dfu_evt_handler</STRONG> (Thumb, 144 bytes, Stack size 56 bytes, ble_conn.o(i.ble_dfu_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = ble_dfu_evt_handler &rArr; nrf_log_frontend_std_1 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_advertising_modes_config_set
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_1
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_conn_state_for_each_connected
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.services_init)
</UL>
<P><STRONG><a name="[73]"></a>ble_evt_handler</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, ble_conn.o(i.ble_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = ble_evt_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_ble_qwr_conn_handle_assign
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_ble_observers3)
</UL>
<P><STRONG><a name="[7c]"></a>buttonless_dfu_sdh_state_observer</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ble_conn.o(i.buttonless_dfu_sdh_state_observer))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = buttonless_dfu_sdh_state_observer &rArr; nrf_pwr_mgmt_shutdown &rArr; shutdown_process &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_pwr_mgmt_shutdown
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(sdh_state_observers0)
</UL>
<P><STRONG><a name="[45]"></a>conn_params_error_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ble_conn.o(i.conn_params_error_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = conn_params_error_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.comm_ble_init)
</UL>
<P><STRONG><a name="[41]"></a>disconnect</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, ble_conn.o(i.disconnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = disconnect &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.ble_dfu_evt_handler)
</UL>
<P><STRONG><a name="[12b]"></a>gap_params_init</STRONG> (Thumb, 134 bytes, Stack size 32 bytes, ble_conn.o(i.gap_params_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = gap_params_init &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_ble_init
</UL>

<P><STRONG><a name="[42]"></a>gatt_evt_handler</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, ble_conn.o(i.gatt_evt_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = gatt_evt_handler &rArr; nrf_log_frontend_std_2 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.comm_ble_init)
</UL>
<P><STRONG><a name="[4f]"></a>nrf_qwr_error_handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ble_conn.o(i.nrf_qwr_error_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = nrf_qwr_error_handler &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.services_init)
</UL>
<P><STRONG><a name="[50]"></a>nus_data_handler</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, ble_conn.o(i.nus_data_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = nus_data_handler &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nrf_log_frontend_std_0
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.services_init)
</UL>
<P><STRONG><a name="[43]"></a>on_adv_evt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ble_conn.o(i.on_adv_evt))
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.comm_ble_init)
</UL>
<P><STRONG><a name="[44]"></a>on_conn_params_evt</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ble_conn.o(i.on_conn_params_evt))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = on_conn_params_evt &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ble_conn.o(i.comm_ble_init)
</UL>
<P><STRONG><a name="[117]"></a>on_connect</STRONG> (Thumb, 106 bytes, Stack size 64 bytes, ble_nus.o(i.on_connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = on_connect &rArr; blcm_link_ctx_get &rArr; ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_is_notification_enabled
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;blcm_link_ctx_get
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_on_ble_evt
</UL>

<P><STRONG><a name="[118]"></a>on_write</STRONG> (Thumb, 132 bytes, Stack size 48 bytes, ble_nus.o(i.on_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = on_write &rArr; blcm_link_ctx_get &rArr; ble_conn_state_conn_idx
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_is_notification_enabled
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;blcm_link_ctx_get
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_nus_on_ble_evt
</UL>

<P><STRONG><a name="[d4]"></a>battery_level_char_add</STRONG> (Thumb, 156 bytes, Stack size 80 bytes, ble_bas.o(i.battery_level_char_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = battery_level_char_add &rArr; characteristic_add
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;descriptor_add
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_report_ref_encode
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_bas_init
</UL>

<P><STRONG><a name="[105]"></a>char_add</STRONG> (Thumb, 90 bytes, Stack size 56 bytes, ble_dis.o(i.char_add))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = char_add &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;characteristic_add
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dis_init
</UL>

<P><STRONG><a name="[107]"></a>pnp_id_encode</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ble_dis.o(i.pnp_id_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = pnp_id_encode &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dis_init
</UL>

<P><STRONG><a name="[106]"></a>sys_id_encode</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, ble_dis.o(i.sys_id_encode))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = sys_id_encode &rArr; app_error_handler_bare &rArr; app_error_fault_handler &rArr; nrf_log_frontend_dequeue &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_error_handler_bare
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dis_init
</UL>

<P><STRONG><a name="[40]"></a>dummy_evt_handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ble_dfu.o(i.dummy_evt_handler))
<BR>[Address Reference Count : 1]<UL><LI> ble_dfu.o(i.ble_dfu_buttonless_init)
</UL>
<P><STRONG><a name="[100]"></a>on_ctrlpt_write</STRONG> (Thumb, 100 bytes, Stack size 48 bytes, ble_dfu.o(i.on_ctrlpt_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = on_ctrlpt_write &rArr; ble_dfu_buttonless_on_ctrl_pt_write &rArr; nrf_log_frontend_std_0 &rArr; std_n &rArr;  nrf_log_frontend_dequeue (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_srv_is_indication_enabled
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ctrl_pt_write
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ble_dfu_buttonless_on_ble_evt
</UL>

<P><STRONG><a name="[96]"></a>_printf_core</STRONG> (Thumb, 436 bytes, Stack size 96 bytes, printf3.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf$3
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$3
</UL>

<P><STRONG><a name="[3d]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printf3.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printf3.o(i.__0snprintf$3)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
