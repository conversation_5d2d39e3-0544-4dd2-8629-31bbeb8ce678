T2220 1487:250.314   SEGGER J-Link V7.94i Log File
T2220 1487:250.436   DLL Compiled: Feb  7 2024 17:04:01
T2220 1487:250.469   Logging started @ 2025-06-24 13:09
T2220 1487:250.499   Process: D:\Keil_mdk\UV4\UV4.exe
T2220 1487:250.543 - 5193.976ms
T2220 1487:250.584 JLINK_SetWarnOutHandler(...)
T2220 1487:250.615 - 0.031ms
T2220 1487:250.649 JLINK_OpenEx(...)
T2220 1487:255.070   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2220 1487:256.647   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T2220 1487:256.943   Decompressing FW timestamp took 225 us
T2220 1487:267.609   Hardware: V9.40
T2220 1487:267.706   S/N: 69409381
T2220 1487:267.763   OEM: SEGGER
T2220 1487:267.820   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
T2220 1487:269.407   Bootloader: (Could not read)
T2220 1487:271.333   TELNET listener socket opened on port 19021
T2220 1487:277.359   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T2220 1487:277.594   WEBSRV Webserver running on local port 19080
T2220 1487:277.748   Looking for J-Link GUI Server exe at: D:\Keil_mdk\ARM\Segger\JLinkGUIServer.exe
T2220 1487:277.787   Looking for J-Link GUI Server exe at: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T2220 1487:277.815   Forking J-Link GUI Server: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T2220 1487:280.238   J-Link GUI Server info: "J-Link GUI server V7.94i "
T2220 1487:280.619 - 29.969ms returns "O.K."
T2220 1487:280.661 JLINK_GetEmuCaps()
T2220 1487:280.676 - 0.015ms returns 0xB9FF7BBF
T2220 1487:280.694 JLINK_TIF_GetAvailable(...)
T2220 1487:281.136 - 0.441ms
T2220 1487:281.173 JLINK_SetErrorOutHandler(...)
T2220 1487:281.186 - 0.013ms
T2220 1487:281.781 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\project\nRF5_SDK_17.1.0_ddde560\nRF5_SDK_17.1.0_ddde560\examples\ble_peripheral\ble_app_template\pca10056\s140\arm5_no_packs\JLinkSettings.ini"", ...). 
T2220 1487:286.307 - 4.523ms returns 0x00
T2220 1487:286.415 JLINK_ExecCommand("Device = nRF52840_xxAA", ...). 
T2220 1487:287.133   Device "NRF52840_XXAA" selected.
T2220 1487:287.817 - 1.362ms returns 0x00
T2220 1487:287.868 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T2220 1487:287.909 - 0.003ms returns 0x01
T2220 1487:287.955 JLINK_GetHardwareVersion()
T2220 1487:287.990 - 0.033ms returns 94000
T2220 1487:288.025 JLINK_GetDLLVersion()
T2220 1487:288.058 - 0.033ms returns 79409
T2220 1487:288.092 JLINK_GetOEMString(...)
T2220 1487:288.127 JLINK_GetFirmwareString(...)
T2220 1487:288.158 - 0.030ms
T2220 1487:288.222 JLINK_GetDLLVersion()
T2220 1487:288.254 - 0.031ms returns 79409
T2220 1487:288.286 JLINK_GetCompileDateTime()
T2220 1487:288.318 - 0.031ms
T2220 1487:288.358 JLINK_GetFirmwareString(...)
T2220 1487:288.392 - 0.033ms
T2220 1487:288.432 JLINK_GetHardwareVersion()
T2220 1487:288.463 - 0.031ms returns 94000
T2220 1487:288.503 JLINK_GetSN()
T2220 1487:288.536 - 0.032ms returns 69409381
T2220 1487:288.575 JLINK_GetOEMString(...)
T2220 1487:288.622 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T2220 1487:290.323 - 1.700ms returns 0x00
T2220 1487:290.357 JLINK_HasError()
T2220 1487:290.376 JLINK_SetSpeed(1000)
T2220 1487:290.710 - 0.333ms
T2220 1487:290.737 JLINK_GetId()
T2220 1487:291.148   InitTarget() start
T2220 1487:291.174    J-Link Script File: Executing InitTarget()
T2220 1487:296.667   InitTarget() end - Took 5.46ms
T2220 1487:298.121   Found SW-DP with ID 0x2BA01477
T2220 1487:303.019   DPIDR: 0x2BA01477
T2220 1487:303.059   CoreSight SoC-400 or earlier
T2220 1487:303.082   Scanning AP map to find all available APs
T2220 1487:305.311   AP[2]: Stopped AP scan as end of AP map has been reached
T2220 1487:305.335   AP[0]: AHB-AP (IDR: 0x24770011)
T2220 1487:305.357   AP[1]: JTAG-AP (IDR: 0x02880000)
T2220 1487:305.379   Iterating through AP map to find AHB-AP to use
T2220 1487:307.478   AP[0]: Core found
T2220 1487:307.505   AP[0]: AHB-AP ROM base: 0xE00FF000
T2220 1487:308.598   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T2220 1487:308.626   Found Cortex-M4 r0p1, Little endian.
T2220 1487:309.497   -- Max. mem block: 0x00010C40
T2220 1487:310.479   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:311.375   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2220 1487:312.370   CPU_ReadMem(4 bytes @ 0x********)
T2220 1487:313.344   FPUnit: 6 code (BP) slots and 2 literal slots
T2220 1487:313.397   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2220 1487:314.483   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:315.641   CPU_ReadMem(4 bytes @ 0xE0001000)
T2220 1487:316.603   CPU_WriteMem(4 bytes @ 0xE0001000)
T2220 1487:317.610   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2220 1487:318.572   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2220 1487:319.554   CPU_ReadMem(4 bytes @ 0xE000ED88)
T2220 1487:320.540   CPU_WriteMem(4 bytes @ 0xE000ED88)
T2220 1487:321.544   CoreSight components:
T2220 1487:321.590   ROMTbl[0] @ E00FF000
T2220 1487:321.630   CPU_ReadMem(64 bytes @ 0xE00FF000)
T2220 1487:323.784   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T2220 1487:325.225   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T2220 1487:325.276   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T2220 1487:326.870   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T2220 1487:326.927   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T2220 1487:328.465   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T2220 1487:328.549   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T2220 1487:330.051   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T2220 1487:330.128   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T2220 1487:331.694   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T2220 1487:331.756   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T2220 1487:333.272   [0][5]: ******** CID B105900D PID 000BB925 ETM
T2220 1487:333.954 - 43.215ms returns 0x2BA01477
T2220 1487:334.039 JLINK_GetDLLVersion()
T2220 1487:334.083 - 0.043ms returns 79409
T2220 1487:334.251 JLINK_CORE_GetFound()
T2220 1487:334.300 - 0.048ms returns 0xE0000FF
T2220 1487:334.352 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2220 1487:334.397   Value=0xE00FF000
T2220 1487:334.463 - 0.112ms returns 0
T2220 1487:334.528 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T2220 1487:334.573   Value=0xE00FF000
T2220 1487:334.637 - 0.109ms returns 0
T2220 1487:334.683 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T2220 1487:334.725   Value=0x********
T2220 1487:334.792 - 0.109ms returns 0
T2220 1487:334.841 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T2220 1487:334.919   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T2220 1487:336.528   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2220 1487:336.626 - 1.785ms returns 32 (0x20)
T2220 1487:336.680 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T2220 1487:336.726   Value=0x00000000
T2220 1487:336.789 - 0.109ms returns 0
T2220 1487:336.833 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T2220 1487:336.877   Value=0x********
T2220 1487:336.936 - 0.103ms returns 0
T2220 1487:336.979 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T2220 1487:337.017   Value=0x********
T2220 1487:337.072 - 0.093ms returns 0
T2220 1487:337.115 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T2220 1487:337.158   Value=0xE0001000
T2220 1487:337.217 - 0.101ms returns 0
T2220 1487:337.259 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T2220 1487:337.298   Value=0x********
T2220 1487:337.360 - 0.101ms returns 0
T2220 1487:337.403 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T2220 1487:337.442   Value=0xE000E000
T2220 1487:337.606 - 0.202ms returns 0
T2220 1487:337.651 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T2220 1487:337.690   Value=0xE000EDF0
T2220 1487:337.750 - 0.098ms returns 0
T2220 1487:337.796 JLINK_ReadMemU32(0xE000EF40, 0x1 Items)
T2220 1487:337.849   CPU_ReadMem(4 bytes @ 0xE000EF40)
T2220 1487:338.967   Data:  21 00 11 10
T2220 1487:339.029 - 1.234ms returns 1 (0x1)
T2220 1487:339.071 JLINK_ReadMemU32(0xE000EF44, 0x1 Items)
T2220 1487:339.119   CPU_ReadMem(4 bytes @ 0xE000EF44)
T2220 1487:340.079   Data:  11 00 00 11
T2220 1487:340.160 - 1.089ms returns 1 (0x1)
T2220 1487:340.213 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T2220 1487:340.252   CPU_ReadMem(4 bytes @ 0xE000ED00)
T2220 1487:341.253   Data:  41 C2 0F 41
T2220 1487:341.310   Debug reg: CPUID
T2220 1487:341.362 - 1.148ms returns 1 (0x1)
T2220 1487:341.400 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T2220 1487:341.437   Value=0x00000000
T2220 1487:341.489 - 0.088ms returns 0
T2220 1487:341.527 JLINK_HasError()
T2220 1487:341.569 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2220 1487:341.605 - 0.036ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2220 1487:341.642 JLINK_Reset()
T2220 1487:341.684   CPU is running
T2220 1487:341.738   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2220 1487:342.789   CPU is running
T2220 1487:342.848   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:344.011   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2220 1487:345.152   Reset: Reset device via AIRCR.SYSRESETREQ.
T2220 1487:345.306   CPU is running
T2220 1487:345.402   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2220 1487:401.720   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:403.122   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:413.928   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:420.899   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:431.885   CPU_WriteMem(4 bytes @ 0x********)
T2220 1487:433.064   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2220 1487:434.278   CPU_ReadMem(4 bytes @ 0xE0001000)
T2220 1487:435.541 - 93.897ms
T2220 1487:435.696 JLINK_HasError()
T2220 1487:435.922 JLINK_ReadReg(R15 (PC))
T2220 1487:436.003 - 0.081ms returns 0x00000A80
T2220 1487:436.063 JLINK_ReadReg(XPSR)
T2220 1487:436.126 - 0.063ms returns 0x01000000
T2220 1487:436.191 JLINK_Halt()
T2220 1487:436.249 - 0.058ms returns 0x00
T2220 1487:436.309 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T2220 1487:436.381   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:437.506   Data:  03 00 03 00
T2220 1487:437.618   Debug reg: DHCSR
T2220 1487:437.740 - 1.431ms returns 1 (0x1)
T2220 1487:437.814 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T2220 1487:437.869   Debug reg: DHCSR
T2220 1487:438.594   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2220 1487:439.796 - 1.981ms returns 0 (0x00000000)
T2220 1487:439.853 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T2220 1487:439.898   Debug reg: DEMCR
T2220 1487:439.976   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:440.997 - 1.144ms returns 0 (0x00000000)
T2220 1487:441.114 JLINK_GetHWStatus(...)
T2220 1487:441.633 - 0.518ms returns 0
T2220 1487:441.712 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T2220 1487:441.754 - 0.041ms returns 0x06
T2220 1487:441.794 JLINK_GetNumBPUnits(Type = 0xF0)
T2220 1487:441.831 - 0.037ms returns 0x2000
T2220 1487:441.873 JLINK_GetNumWPUnits()
T2220 1487:441.910 - 0.037ms returns 4
T2220 1487:441.970 JLINK_GetSpeed()
T2220 1487:442.013 - 0.043ms returns 1000
T2220 1487:442.066 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2220 1487:442.112   CPU_ReadMem(4 bytes @ 0xE000E004)
T2220 1487:443.224   Data:  01 00 00 00
T2220 1487:443.323 - 1.256ms returns 1 (0x1)
T2220 1487:443.370 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T2220 1487:443.426   CPU_ReadMem(4 bytes @ 0xE000E004)
T2220 1487:444.494   Data:  01 00 00 00
T2220 1487:444.566 - 1.196ms returns 1 (0x1)
T2220 1487:444.610 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T2220 1487:444.646   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T2220 1487:444.709   CPU_WriteMem(28 bytes @ 0xE0001000)
T2220 1487:446.213 - 1.603ms returns 0x1C
T2220 1487:446.258 JLINK_HasError()
T2220 1487:446.297 JLINK_ReadReg(R15 (PC))
T2220 1487:446.335 - 0.037ms returns 0x00000A80
T2220 1487:446.371 JLINK_ReadReg(XPSR)
T2220 1487:446.407 - 0.036ms returns 0x01000000
T2220 1487:557.421 JLINK_HasError()
T2220 1487:557.517 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T2220 1487:557.555 - 0.037ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T2220 1487:557.593 JLINK_Reset()
T2220 1487:557.668   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T2220 1487:558.798   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:559.994   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T2220 1487:561.101   Reset: Reset device via AIRCR.SYSRESETREQ.
T2220 1487:561.172   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T2220 1487:616.578   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:617.747   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:618.825   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T2220 1487:625.957   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T2220 1487:636.096   CPU_WriteMem(4 bytes @ 0x********)
T2220 1487:637.228   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T2220 1487:638.331   CPU_ReadMem(4 bytes @ 0xE0001000)
T2220 1487:639.487 - 81.892ms
T2220 1487:639.794 JLINK_HasError()
T2220 1487:639.874 JLINK_ReadReg(R15 (PC))
T2220 1487:639.954 - 0.079ms returns 0x00000A80
T2220 1487:640.020 JLINK_ReadReg(XPSR)
T2220 1487:640.092 - 0.071ms returns 0x01000000
T2220 1487:640.779 JLINK_ReadMemEx(0x00000A80, 0x3C Bytes, Flags = 0x02000000)
T2220 1487:640.885   CPU_ReadMem(64 bytes @ 0x00000A80)
T2220 1487:643.127    -- Updating C cache (64 bytes @ 0x00000A80)
T2220 1487:643.214    -- Read from C cache (60 bytes @ 0x00000A80)
T2220 1487:643.296   Data:  06 4B 18 47 06 4A 10 60 01 68 81 F3 08 88 40 68 ...
T2220 1487:643.374 - 2.596ms returns 60 (0x3C)
T2220 1488:867.305 JLINK_HasError()
T2220 1488:867.338 JLINK_ReadReg(R0)
T2220 1488:868.369 - 1.030ms returns 0x00000000
T2220 1488:868.405 JLINK_ReadReg(R1)
T2220 1488:868.420 - 0.014ms returns 0x00000000
T2220 1488:868.434 JLINK_ReadReg(R2)
T2220 1488:868.448 - 0.014ms returns 0x00000000
T2220 1488:868.463 JLINK_ReadReg(R3)
T2220 1488:868.476 - 0.013ms returns 0x00000000
T2220 1488:868.489 JLINK_ReadReg(R4)
T2220 1488:868.502 - 0.013ms returns 0x00000000
T2220 1488:868.516 JLINK_ReadReg(R5)
T2220 1488:868.529 - 0.013ms returns 0x00000000
T2220 1488:868.542 JLINK_ReadReg(R6)
T2220 1488:868.555 - 0.013ms returns 0x00000000
T2220 1488:868.569 JLINK_ReadReg(R7)
T2220 1488:868.582 - 0.013ms returns 0x00000000
T2220 1488:868.596 JLINK_ReadReg(R8)
T2220 1488:868.609 - 0.013ms returns 0x00000000
T2220 1488:868.622 JLINK_ReadReg(R9)
T2220 1488:868.636 - 0.013ms returns 0x00000000
T2220 1488:868.649 JLINK_ReadReg(R10)
T2220 1488:868.663 - 0.013ms returns 0x00000000
T2220 1488:868.676 JLINK_ReadReg(R11)
T2220 1488:868.699 - 0.022ms returns 0x00000000
T2220 1488:868.712 JLINK_ReadReg(R12)
T2220 1488:868.725 - 0.013ms returns 0x00000000
T2220 1488:868.739 JLINK_ReadReg(R13 (SP))
T2220 1488:868.752 - 0.013ms returns 0x20000400
T2220 1488:868.766 JLINK_ReadReg(R14)
T2220 1488:868.779 - 0.012ms returns 0xFFFFFFFF
T2220 1488:868.793 JLINK_ReadReg(R15 (PC))
T2220 1488:868.807 - 0.013ms returns 0x00000A80
T2220 1488:868.820 JLINK_ReadReg(XPSR)
T2220 1488:868.834 - 0.013ms returns 0x01000000
T2220 1488:868.847 JLINK_ReadReg(MSP)
T2220 1488:868.860 - 0.013ms returns 0x20000400
T2220 1488:868.874 JLINK_ReadReg(PSP)
T2220 1488:868.887 - 0.013ms returns 0x00000000
T2220 1488:868.901 JLINK_ReadReg(CFBP)
T2220 1488:868.914 - 0.013ms returns 0x00000000
T2220 1488:868.934 JLINK_ReadReg(FPSCR)
T2220 1488:883.067 - 14.137ms returns 0x00000000
T2220 1488:883.117 JLINK_ReadReg(FPS0)
T2220 1488:883.135 - 0.018ms returns 0x00000000
T2220 1488:883.150 JLINK_ReadReg(FPS1)
T2220 1488:883.164 - 0.013ms returns 0x00000000
T2220 1488:883.177 JLINK_ReadReg(FPS2)
T2220 1488:883.191 - 0.013ms returns 0x00000000
T2220 1488:883.204 JLINK_ReadReg(FPS3)
T2220 1488:883.217 - 0.012ms returns 0x00000000
T2220 1488:883.231 JLINK_ReadReg(FPS4)
T2220 1488:883.244 - 0.013ms returns 0x00000000
T2220 1488:883.258 JLINK_ReadReg(FPS5)
T2220 1488:883.271 - 0.012ms returns 0x00000000
T2220 1488:883.284 JLINK_ReadReg(FPS6)
T2220 1488:883.299 - 0.013ms returns 0x00000000
T2220 1488:883.312 JLINK_ReadReg(FPS7)
T2220 1488:883.325 - 0.013ms returns 0x00000000
T2220 1488:883.339 JLINK_ReadReg(FPS8)
T2220 1488:883.351 - 0.012ms returns 0x00000000
T2220 1488:883.365 JLINK_ReadReg(FPS9)
T2220 1488:883.378 - 0.012ms returns 0x00000000
T2220 1488:883.391 JLINK_ReadReg(FPS10)
T2220 1488:883.453 - 0.061ms returns 0x00000000
T2220 1488:883.472 JLINK_ReadReg(FPS11)
T2220 1488:883.486 - 0.013ms returns 0x00000000
T2220 1488:883.499 JLINK_ReadReg(FPS12)
T2220 1488:883.512 - 0.013ms returns 0x00000000
T2220 1488:883.526 JLINK_ReadReg(FPS13)
T2220 1488:883.538 - 0.012ms returns 0x00000000
T2220 1488:883.551 JLINK_ReadReg(FPS14)
T2220 1488:883.565 - 0.012ms returns 0x00000000
T2220 1488:883.584 JLINK_ReadReg(FPS15)
T2220 1488:883.597 - 0.013ms returns 0x00000000
T2220 1488:883.610 JLINK_ReadReg(FPS16)
T2220 1488:883.624 - 0.013ms returns 0x00000000
T2220 1488:883.637 JLINK_ReadReg(FPS17)
T2220 1488:883.650 - 0.013ms returns 0x00000000
T2220 1488:883.665 JLINK_ReadReg(FPS18)
T2220 1488:883.677 - 0.012ms returns 0x00000000
T2220 1488:883.691 JLINK_ReadReg(FPS19)
T2220 1488:883.704 - 0.013ms returns 0x00000000
T2220 1488:883.718 JLINK_ReadReg(FPS20)
T2220 1488:883.731 - 0.013ms returns 0x00000000
T2220 1488:883.745 JLINK_ReadReg(FPS21)
T2220 1488:883.757 - 0.012ms returns 0x00000000
T2220 1488:883.771 JLINK_ReadReg(FPS22)
T2220 1488:883.784 - 0.012ms returns 0x00000000
T2220 1488:883.798 JLINK_ReadReg(FPS23)
T2220 1488:883.810 - 0.012ms returns 0x00000000
T2220 1488:883.824 JLINK_ReadReg(FPS24)
T2220 1488:883.837 - 0.013ms returns 0x00000000
T2220 1488:883.850 JLINK_ReadReg(FPS25)
T2220 1488:883.863 - 0.012ms returns 0x00000000
T2220 1488:883.877 JLINK_ReadReg(FPS26)
T2220 1488:883.890 - 0.013ms returns 0x00000000
T2220 1488:883.903 JLINK_ReadReg(FPS27)
T2220 1488:883.916 - 0.012ms returns 0x00000000
T2220 1488:883.931 JLINK_ReadReg(FPS28)
T2220 1488:883.944 - 0.013ms returns 0x00000000
T2220 1488:883.958 JLINK_ReadReg(FPS29)
T2220 1488:883.971 - 0.013ms returns 0x00000000
T2220 1488:883.985 JLINK_ReadReg(FPS30)
T2220 1488:883.998 - 0.012ms returns 0x00000000
T2220 1488:884.011 JLINK_ReadReg(FPS31)
T2220 1488:884.025 - 0.013ms returns 0x00000000
T6838 1488:943.002 JLINK_ReadMemEx(0x00000A80, 0x2 Bytes, Flags = 0x02000000)
T6838 1488:943.062    -- Read from C cache (2 bytes @ 0x00000A80)
T6838 1488:943.133   Data:  06 4B
T6838 1488:943.199 - 0.196ms returns 2 (0x2)
T6838 1488:943.248 JLINK_HasError()
T6838 1488:943.311 JLINK_SetBPEx(Addr = 0x0002AF70, Type = 0xFFFFFFF2)
T6838 1488:943.365 - 0.054ms returns 0x00000001
T6838 1488:943.414 JLINK_HasError()
T6838 1488:943.460 JLINK_SetBPEx(Addr = 0x0002945E, Type = 0xFFFFFFF2)
T6838 1488:943.509 - 0.048ms returns 0x00000002
T6838 1488:943.555 JLINK_HasError()
T6838 1488:943.600 JLINK_SetBPEx(Addr = 0x00029508, Type = 0xFFFFFFF2)
T6838 1488:943.645 - 0.046ms returns 0x00000003
T6838 1488:943.691 JLINK_HasError()
T6838 1488:943.738 JLINK_SetBPEx(Addr = 0x0002D47E, Type = 0xFFFFFFF2)
T6838 1488:943.783 - 0.045ms returns 0x00000004
T6838 1488:943.831 JLINK_HasError()
T6838 1488:943.879 JLINK_SetBPEx(Addr = 0x00029476, Type = 0xFFFFFFF2)
T6838 1488:943.918 - 0.039ms returns 0x00000005
T6838 1488:943.933 JLINK_HasError()
T6838 1488:943.948 JLINK_HasError()
T6838 1488:943.964 JLINK_Go()
T6838 1488:943.984   CPU_WriteMem(4 bytes @ 0x********)
T6838 1488:944.961   CPU_WriteMem(4 bytes @ 0x********)
T6838 1488:945.966   CPU_WriteMem(4 bytes @ 0x********)
T6838 1488:946.972   CPU_WriteMem(4 bytes @ 0x********)
T6838 1488:947.914   CPU_WriteMem(4 bytes @ 0x********)
T6838 1488:948.840   CPU_ReadMem(4 bytes @ 0xE0001000)
T6838 1488:949.750   CPU_WriteMem(4 bytes @ 0xE0002008)
T6838 1488:949.781   CPU_WriteMem(4 bytes @ 0xE000200C)
T6838 1488:949.802   CPU_WriteMem(4 bytes @ 0xE0002010)
T6838 1488:949.822   CPU_WriteMem(4 bytes @ 0xE0002014)
T6838 1488:949.841   CPU_WriteMem(4 bytes @ 0xE0002018)
T6838 1488:949.861   CPU_WriteMem(4 bytes @ 0xE000201C)
T6838 1488:953.753   CPU_WriteMem(4 bytes @ 0xE0001004)
T6838 1488:964.939   Memory map 'after startup completion point' is active
T6838 1488:964.983 - 21.019ms
T6838 1489:065.487 JLINK_HasError()
T6838 1489:065.617 JLINK_IsHalted()
T6838 1489:074.867 - 9.247ms returns TRUE
T6838 1489:074.968 JLINK_HasError()
T6838 1489:075.015 JLINK_Halt()
T6838 1489:075.066 - 0.050ms returns 0x00
T6838 1489:075.125 JLINK_IsHalted()
T6838 1489:075.167 - 0.042ms returns TRUE
T6838 1489:075.215 JLINK_IsHalted()
T6838 1489:075.256 - 0.040ms returns TRUE
T6838 1489:075.404 JLINK_IsHalted()
T6838 1489:075.444 - 0.040ms returns TRUE
T6838 1489:075.487 JLINK_HasError()
T6838 1489:075.529 JLINK_ReadReg(R15 (PC))
T6838 1489:075.574 - 0.044ms returns 0x0002AF70
T6838 1489:075.618 JLINK_ReadReg(XPSR)
T6838 1489:075.658 - 0.040ms returns 0x61000000
T6838 1489:075.707 JLINK_HasError()
T6838 1489:075.758 JLINK_ClrBPEx(BPHandle = 0x00000001)
T6838 1489:075.800 - 0.041ms returns 0x00
T6838 1489:075.842 JLINK_HasError()
T6838 1489:075.885 JLINK_ClrBPEx(BPHandle = 0x00000002)
T6838 1489:075.922 - 0.037ms returns 0x00
T6838 1489:075.964 JLINK_HasError()
T6838 1489:076.008 JLINK_ClrBPEx(BPHandle = 0x00000003)
T6838 1489:076.050 - 0.042ms returns 0x00
T6838 1489:076.092 JLINK_HasError()
T6838 1489:076.133 JLINK_ClrBPEx(BPHandle = 0x00000004)
T6838 1489:076.175 - 0.042ms returns 0x00
T6838 1489:076.221 JLINK_HasError()
T6838 1489:076.264 JLINK_ClrBPEx(BPHandle = 0x00000005)
T6838 1489:076.303 - 0.039ms returns 0x00
T6838 1489:076.347 JLINK_HasError()
T6838 1489:076.390 JLINK_HasError()
T6838 1489:076.434 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6838 1489:076.486   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6838 1489:077.701   Data:  02 00 00 00
T6838 1489:077.773 - 1.338ms returns 1 (0x1)
T6838 1489:077.826 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6838 1489:077.878   CPU_ReadMem(4 bytes @ 0xE0001028)
T6838 1489:078.853   Data:  00 00 00 00
T6838 1489:078.934   Debug reg: DWT_FUNC[0]
T6838 1489:079.006 - 1.180ms returns 1 (0x1)
T6838 1489:079.058 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6838 1489:079.111   CPU_ReadMem(4 bytes @ 0xE0001038)
T6838 1489:080.311   Data:  00 02 00 00
T6838 1489:080.446   Debug reg: DWT_FUNC[1]
T6838 1489:080.532 - 1.472ms returns 1 (0x1)
T6838 1489:080.607 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6838 1489:080.697   CPU_ReadMem(4 bytes @ 0xE0001048)
T6838 1489:081.884   Data:  00 00 00 00
T6838 1489:081.961   Debug reg: DWT_FUNC[2]
T6838 1489:082.031 - 1.424ms returns 1 (0x1)
T6838 1489:082.082 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6838 1489:082.132   CPU_ReadMem(4 bytes @ 0xE0001058)
T6838 1489:083.184   Data:  00 00 00 00
T6838 1489:083.269   Debug reg: DWT_FUNC[3]
T6838 1489:083.319 - 1.236ms returns 1 (0x1)
T6838 1489:083.406 JLINK_HasError()
T6838 1489:083.442 JLINK_ReadReg(R0)
T6838 1489:083.481 - 0.038ms returns 0x0002AF71
T6838 1489:083.513 JLINK_ReadReg(R1)
T6838 1489:083.547 - 0.033ms returns 0x20005B48
T6838 1489:083.579 JLINK_ReadReg(R2)
T6838 1489:083.611 - 0.031ms returns 0x00000000
T6838 1489:083.643 JLINK_ReadReg(R3)
T6838 1489:083.680 - 0.036ms returns 0x00027953
T6838 1489:083.724 JLINK_ReadReg(R4)
T6838 1489:083.768 - 0.043ms returns 0x000318F4
T6838 1489:083.808 JLINK_ReadReg(R5)
T6838 1489:083.850 - 0.041ms returns 0x000318F4
T6838 1489:083.893 JLINK_ReadReg(R6)
T6838 1489:083.933 - 0.039ms returns 0x10001000
T6838 1489:083.975 JLINK_ReadReg(R7)
T6838 1489:084.015 - 0.039ms returns 0x00000000
T6838 1489:084.056 JLINK_ReadReg(R8)
T6838 1489:084.096 - 0.039ms returns 0x00000000
T6838 1489:084.135 JLINK_ReadReg(R9)
T6838 1489:084.174 - 0.039ms returns 0x00000000
T6838 1489:084.214 JLINK_ReadReg(R10)
T6838 1489:084.255 - 0.041ms returns 0x00000000
T6838 1489:084.298 JLINK_ReadReg(R11)
T6838 1489:084.338 - 0.039ms returns 0x00000000
T6838 1489:084.379 JLINK_ReadReg(R12)
T6838 1489:084.419 - 0.040ms returns 0x00000000
T6838 1489:084.459 JLINK_ReadReg(R13 (SP))
T6838 1489:084.501 - 0.042ms returns 0x20005B48
T6838 1489:084.543 JLINK_ReadReg(R14)
T6838 1489:084.584 - 0.041ms returns 0x00027495
T6838 1489:084.627 JLINK_ReadReg(R15 (PC))
T6838 1489:084.762 - 0.135ms returns 0x0002AF70
T6838 1489:084.856 JLINK_ReadReg(XPSR)
T6838 1489:084.915 - 0.059ms returns 0x61000000
T6838 1489:084.956 JLINK_ReadReg(MSP)
T6838 1489:084.994 - 0.037ms returns 0x20005B48
T6838 1489:085.035 JLINK_ReadReg(PSP)
T6838 1489:085.087 - 0.051ms returns 0x00000000
T6838 1489:085.127 JLINK_ReadReg(CFBP)
T6838 1489:085.167 - 0.039ms returns 0x00000000
T6838 1489:085.206 JLINK_ReadReg(FPSCR)
T6838 1489:099.468 - 14.259ms returns 0x00000000
T6838 1489:099.569 JLINK_ReadReg(FPS0)
T6838 1489:099.622 - 0.053ms returns 0x00000000
T6838 1489:099.665 JLINK_ReadReg(FPS1)
T6838 1489:099.711 - 0.044ms returns 0x00000000
T6838 1489:099.754 JLINK_ReadReg(FPS2)
T6838 1489:099.794 - 0.039ms returns 0x00000000
T6838 1489:099.839 JLINK_ReadReg(FPS3)
T6838 1489:099.879 - 0.039ms returns 0x00000000
T6838 1489:099.933 JLINK_ReadReg(FPS4)
T6838 1489:099.974 - 0.040ms returns 0x00000000
T6838 1489:100.133 JLINK_ReadReg(FPS5)
T6838 1489:100.242 - 0.108ms returns 0x00000000
T6838 1489:100.288 JLINK_ReadReg(FPS6)
T6838 1489:100.329 - 0.041ms returns 0x00000000
T6838 1489:100.364 JLINK_ReadReg(FPS7)
T6838 1489:100.386 - 0.022ms returns 0x00000000
T6838 1489:100.409 JLINK_ReadReg(FPS8)
T6838 1489:100.430 - 0.021ms returns 0x00000000
T6838 1489:100.452 JLINK_ReadReg(FPS9)
T6838 1489:100.473 - 0.021ms returns 0x00000000
T6838 1489:100.494 JLINK_ReadReg(FPS10)
T6838 1489:100.515 - 0.021ms returns 0x00000000
T6838 1489:100.537 JLINK_ReadReg(FPS11)
T6838 1489:100.558 - 0.021ms returns 0x00000000
T6838 1489:100.581 JLINK_ReadReg(FPS12)
T6838 1489:100.601 - 0.019ms returns 0x00000000
T6838 1489:100.621 JLINK_ReadReg(FPS13)
T6838 1489:100.642 - 0.020ms returns 0x00000000
T6838 1489:100.664 JLINK_ReadReg(FPS14)
T6838 1489:100.686 - 0.021ms returns 0x00000000
T6838 1489:100.709 JLINK_ReadReg(FPS15)
T6838 1489:100.728 - 0.019ms returns 0x00000000
T6838 1489:100.749 JLINK_ReadReg(FPS16)
T6838 1489:100.771 - 0.021ms returns 0x00000000
T6838 1489:100.792 JLINK_ReadReg(FPS17)
T6838 1489:100.814 - 0.022ms returns 0x00000000
T6838 1489:100.837 JLINK_ReadReg(FPS18)
T6838 1489:100.861 - 0.024ms returns 0x00000000
T6838 1489:100.903 JLINK_ReadReg(FPS19)
T6838 1489:100.946 - 0.042ms returns 0x00000000
T6838 1489:100.986 JLINK_ReadReg(FPS20)
T6838 1489:101.021 - 0.035ms returns 0x00000000
T6838 1489:101.061 JLINK_ReadReg(FPS21)
T6838 1489:101.100 - 0.038ms returns 0x00000000
T6838 1489:101.137 JLINK_ReadReg(FPS22)
T6838 1489:101.174 - 0.037ms returns 0x00000000
T6838 1489:101.214 JLINK_ReadReg(FPS23)
T6838 1489:101.253 - 0.038ms returns 0x00000000
T6838 1489:101.293 JLINK_ReadReg(FPS24)
T6838 1489:101.413 - 0.119ms returns 0x00000000
T6838 1489:101.511 JLINK_ReadReg(FPS25)
T6838 1489:101.559 - 0.048ms returns 0x00000000
T6838 1489:101.601 JLINK_ReadReg(FPS26)
T6838 1489:101.640 - 0.038ms returns 0x00000000
T6838 1489:101.680 JLINK_ReadReg(FPS27)
T6838 1489:101.720 - 0.040ms returns 0x00000000
T6838 1489:101.759 JLINK_ReadReg(FPS28)
T6838 1489:101.798 - 0.039ms returns 0x00000000
T6838 1489:101.842 JLINK_ReadReg(FPS29)
T6838 1489:101.882 - 0.039ms returns 0x00000000
T6838 1489:101.903 JLINK_ReadReg(FPS30)
T6838 1489:101.924 - 0.020ms returns 0x00000000
T6838 1489:101.944 JLINK_ReadReg(FPS31)
T6838 1489:101.965 - 0.020ms returns 0x00000000
T2220 1489:103.007 JLINK_ReadMemEx(0x00000000, 0x1 Bytes, Flags = 0x02000000)
T2220 1489:103.088   CPU_ReadMem(64 bytes @ 0x00000000)
T2220 1489:105.342    -- Updating C cache (64 bytes @ 0x00000000)
T2220 1489:105.423    -- Read from C cache (1 bytes @ 0x00000000)
T2220 1489:105.486   Data:  00
T2220 1489:105.549 - 2.541ms returns 1 (0x1)
T2220 1489:105.608 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T2220 1489:105.653    -- Read from C cache (4 bytes @ 0x00000000)
T2220 1489:105.713   Data:  00 04 00 20
T2220 1489:105.771 - 0.163ms returns 4 (0x4)
T2220 1489:113.730 JLINK_HasError()
T2220 1489:113.797 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2220 1489:113.834   CPU_ReadMem(4 bytes @ 0xE0001004)
T2220 1489:114.968   Data:  94 65 00 00
T2220 1489:115.090   Debug reg: DWT_CYCCNT
T2220 1489:115.162 - 1.364ms returns 1 (0x1)
T6838 1493:975.323 JLINK_ReadMemEx(0x0002AF70, 0x2 Bytes, Flags = 0x02000000)
T6838 1493:975.428   CPU_ReadMem(64 bytes @ 0x0002AF40)
T6838 1493:977.553    -- Updating C cache (64 bytes @ 0x0002AF40)
T6838 1493:977.619    -- Read from C cache (2 bytes @ 0x0002AF70)
T6838 1493:977.666   Data:  8A B0
T6838 1493:977.711 - 2.388ms returns 2 (0x2)
T6838 1493:977.747 JLINK_HasError()
T6838 1493:977.781 JLINK_SetBPEx(Addr = 0x0002945E, Type = 0xFFFFFFF2)
T6838 1493:977.816 - 0.034ms returns 0x00000006
T6838 1493:977.848 JLINK_HasError()
T6838 1493:977.880 JLINK_SetBPEx(Addr = 0x00029508, Type = 0xFFFFFFF2)
T6838 1493:977.909 - 0.029ms returns 0x00000007
T6838 1493:977.939 JLINK_HasError()
T6838 1493:977.968 JLINK_SetBPEx(Addr = 0x0002D47E, Type = 0xFFFFFFF2)
T6838 1493:977.997 - 0.029ms returns 0x00000008
T6838 1493:978.025 JLINK_HasError()
T6838 1493:978.056 JLINK_SetBPEx(Addr = 0x00029476, Type = 0xFFFFFFF2)
T6838 1493:978.085 - 0.029ms returns 0x00000009
T6838 1493:978.115 JLINK_HasError()
T6838 1493:978.145 JLINK_HasError()
T6838 1493:978.175 JLINK_Go()
T6838 1493:979.254   CPU_WriteMem(4 bytes @ 0x********)
T6838 1493:980.350   CPU_WriteMem(4 bytes @ 0x********)
T6838 1493:981.386   CPU_WriteMem(4 bytes @ 0x********)
T6838 1493:982.389   CPU_WriteMem(4 bytes @ 0x********)
T6838 1493:983.313   CPU_ReadMem(4 bytes @ 0xE0001000)
T6838 1493:984.302   CPU_WriteMem(4 bytes @ 0xE0002008)
T6838 1493:984.350   CPU_WriteMem(4 bytes @ 0xE000200C)
T6838 1493:984.394   CPU_WriteMem(4 bytes @ 0xE0002010)
T6838 1493:984.439   CPU_WriteMem(4 bytes @ 0xE0002014)
T6838 1493:984.482   CPU_WriteMem(4 bytes @ 0xE0002018)
T6838 1493:989.147 - 10.968ms
T6838 1494:089.404 JLINK_HasError()
T6838 1494:089.545 JLINK_IsHalted()
T6838 1494:090.700 - 1.151ms returns FALSE
T6838 1494:191.455 JLINK_HasError()
T6838 1494:191.607 JLINK_IsHalted()
T6838 1494:192.714 - 1.104ms returns FALSE
T6838 1494:293.298 JLINK_HasError()
T6838 1494:293.512 JLINK_IsHalted()
T6838 1494:294.602 - 1.087ms returns FALSE
T6838 1494:395.227 JLINK_HasError()
T6838 1494:395.367 JLINK_IsHalted()
T6838 1494:396.581 - 1.212ms returns FALSE
T6838 1494:496.898 JLINK_HasError()
T6838 1494:497.046 JLINK_IsHalted()
T6838 1494:498.534 - 1.485ms returns FALSE
T6838 1494:598.753 JLINK_HasError()
T6838 1494:598.954 JLINK_IsHalted()
T6838 1494:600.154 - 1.195ms returns FALSE
T6838 1494:700.847 JLINK_HasError()
T6838 1494:700.944 JLINK_IsHalted()
T6838 1494:710.028 - 9.081ms returns TRUE
T6838 1494:710.183 JLINK_HasError()
T6838 1494:710.260 JLINK_Halt()
T6838 1494:710.325 - 0.064ms returns 0x00
T6838 1494:710.399 JLINK_IsHalted()
T6838 1494:710.463 - 0.063ms returns TRUE
T6838 1494:710.543 JLINK_IsHalted()
T6838 1494:710.605 - 0.061ms returns TRUE
T6838 1494:710.672 JLINK_IsHalted()
T6838 1494:710.733 - 0.060ms returns TRUE
T6838 1494:710.815 JLINK_HasError()
T6838 1494:710.889 JLINK_ReadReg(R15 (PC))
T6838 1494:710.960 - 0.070ms returns 0x00027ABC
T6838 1494:711.028 JLINK_ReadReg(XPSR)
T6838 1494:711.087 - 0.058ms returns 0x81000000
T6838 1494:711.260 JLINK_HasError()
T6838 1494:711.321 JLINK_ClrBPEx(BPHandle = 0x00000006)
T6838 1494:711.378 - 0.057ms returns 0x00
T6838 1494:711.461 JLINK_HasError()
T6838 1494:711.519 JLINK_ClrBPEx(BPHandle = 0x00000007)
T6838 1494:711.574 - 0.055ms returns 0x00
T6838 1494:711.628 JLINK_HasError()
T6838 1494:711.686 JLINK_ClrBPEx(BPHandle = 0x00000008)
T6838 1494:711.737 - 0.050ms returns 0x00
T6838 1494:711.792 JLINK_HasError()
T6838 1494:711.847 JLINK_ClrBPEx(BPHandle = 0x00000009)
T6838 1494:711.898 - 0.050ms returns 0x00
T6838 1494:711.948 JLINK_HasError()
T6838 1494:711.998 JLINK_HasError()
T6838 1494:712.049 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T6838 1494:712.111   CPU_ReadMem(4 bytes @ 0xE000ED30)
T6838 1494:713.131   Data:  02 00 00 00
T6838 1494:713.241 - 1.191ms returns 1 (0x1)
T6838 1494:713.297 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T6838 1494:713.359   CPU_ReadMem(4 bytes @ 0xE0001028)
T6838 1494:714.423   Data:  00 00 00 00
T6838 1494:714.492   Debug reg: DWT_FUNC[0]
T6838 1494:714.553 - 1.257ms returns 1 (0x1)
T6838 1494:714.599 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T6838 1494:714.773   CPU_ReadMem(4 bytes @ 0xE0001038)
T6838 1494:715.787   Data:  00 02 00 00
T6838 1494:715.897   Debug reg: DWT_FUNC[1]
T6838 1494:715.955 - 1.355ms returns 1 (0x1)
T6838 1494:716.002 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T6838 1494:716.055   CPU_ReadMem(4 bytes @ 0xE0001048)
T6838 1494:717.049   Data:  00 00 00 00
T6838 1494:717.148   Debug reg: DWT_FUNC[2]
T6838 1494:717.207 - 1.205ms returns 1 (0x1)
T6838 1494:717.254 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T6838 1494:717.307   CPU_ReadMem(4 bytes @ 0xE0001058)
T6838 1494:718.353   Data:  00 00 00 00
T6838 1494:718.420   Debug reg: DWT_FUNC[3]
T6838 1494:718.460 - 1.206ms returns 1 (0x1)
T6838 1494:718.551 JLINK_HasError()
T6838 1494:718.582 JLINK_ReadReg(R0)
T6838 1494:718.613 - 0.031ms returns 0x80000000
T6838 1494:718.641 JLINK_ReadReg(R1)
T6838 1494:718.667 - 0.026ms returns 0x0000000E
T6838 1494:718.696 JLINK_ReadReg(R2)
T6838 1494:718.724 - 0.027ms returns 0x00000000
T6838 1494:718.751 JLINK_ReadReg(R3)
T6838 1494:718.777 - 0.025ms returns 0x0000000C
T6838 1494:718.805 JLINK_ReadReg(R4)
T6838 1494:718.832 - 0.027ms returns 0x00000016
T6838 1494:718.860 JLINK_ReadReg(R5)
T6838 1494:718.887 - 0.026ms returns 0x000318F4
T6838 1494:718.915 JLINK_ReadReg(R6)
T6838 1494:718.942 - 0.027ms returns 0x10001000
T6838 1494:718.956 JLINK_ReadReg(R7)
T6838 1494:718.970 - 0.013ms returns 0x00000000
T6838 1494:718.984 JLINK_ReadReg(R8)
T6838 1494:718.996 - 0.012ms returns 0x00000000
T6838 1494:719.011 JLINK_ReadReg(R9)
T6838 1494:719.024 - 0.013ms returns 0x00000000
T6838 1494:719.038 JLINK_ReadReg(R10)
T6838 1494:719.052 - 0.013ms returns 0x00000000
T6838 1494:719.065 JLINK_ReadReg(R11)
T6838 1494:719.079 - 0.013ms returns 0x00000000
T6838 1494:719.092 JLINK_ReadReg(R12)
T6838 1494:719.105 - 0.012ms returns 0x00000000
T6838 1494:719.119 JLINK_ReadReg(R13 (SP))
T6838 1494:719.132 - 0.013ms returns 0x20005A58
T6838 1494:719.146 JLINK_ReadReg(R14)
T6838 1494:719.160 - 0.013ms returns 0x0002F491
T6838 1494:719.173 JLINK_ReadReg(R15 (PC))
T6838 1494:719.186 - 0.013ms returns 0x00027ABC
T6838 1494:719.200 JLINK_ReadReg(XPSR)
T6838 1494:719.214 - 0.013ms returns 0x81000000
T6838 1494:719.228 JLINK_ReadReg(MSP)
T6838 1494:719.241 - 0.013ms returns 0x20005A58
T6838 1494:719.254 JLINK_ReadReg(PSP)
T6838 1494:719.267 - 0.013ms returns 0x00000000
T6838 1494:719.281 JLINK_ReadReg(CFBP)
T6838 1494:719.294 - 0.012ms returns 0x00000001
T6838 1494:719.308 JLINK_ReadReg(FPSCR)
T6838 1494:733.311 - 14.000ms returns 0x00000000
T6838 1494:733.412 JLINK_ReadReg(FPS0)
T6838 1494:733.459 - 0.047ms returns 0x00000000
T6838 1494:733.500 JLINK_ReadReg(FPS1)
T6838 1494:733.537 - 0.037ms returns 0x00000000
T6838 1494:733.577 JLINK_ReadReg(FPS2)
T6838 1494:733.623 - 0.045ms returns 0x00000000
T6838 1494:733.662 JLINK_ReadReg(FPS3)
T6838 1494:733.699 - 0.036ms returns 0x00000000
T6838 1494:733.735 JLINK_ReadReg(FPS4)
T6838 1494:733.771 - 0.036ms returns 0x00000000
T6838 1494:733.807 JLINK_ReadReg(FPS5)
T6838 1494:733.844 - 0.036ms returns 0x00000000
T6838 1494:733.881 JLINK_ReadReg(FPS6)
T6838 1494:733.918 - 0.036ms returns 0x00000000
T6838 1494:733.955 JLINK_ReadReg(FPS7)
T6838 1494:733.992 - 0.035ms returns 0x00000000
T6838 1494:734.030 JLINK_ReadReg(FPS8)
T6838 1494:734.066 - 0.036ms returns 0x00000000
T6838 1494:734.104 JLINK_ReadReg(FPS9)
T6838 1494:734.139 - 0.034ms returns 0x00000000
T6838 1494:734.175 JLINK_ReadReg(FPS10)
T6838 1494:734.213 - 0.037ms returns 0x00000000
T6838 1494:734.250 JLINK_ReadReg(FPS11)
T6838 1494:734.286 - 0.036ms returns 0x00000000
T6838 1494:734.326 JLINK_ReadReg(FPS12)
T6838 1494:734.360 - 0.034ms returns 0x00000000
T6838 1494:734.398 JLINK_ReadReg(FPS13)
T6838 1494:734.434 - 0.036ms returns 0x00000000
T6838 1494:734.471 JLINK_ReadReg(FPS14)
T6838 1494:734.508 - 0.037ms returns 0x00000000
T6838 1494:734.544 JLINK_ReadReg(FPS15)
T6838 1494:734.669 - 0.124ms returns 0x00000000
T6838 1494:734.708 JLINK_ReadReg(FPS16)
T6838 1494:734.747 - 0.038ms returns 0x00000000
T6838 1494:734.906 JLINK_ReadReg(FPS17)
T6838 1494:734.953 - 0.047ms returns 0x00000000
T6838 1494:735.029 JLINK_ReadReg(FPS18)
T6838 1494:735.068 - 0.039ms returns 0x00000000
T6838 1494:735.107 JLINK_ReadReg(FPS19)
T6838 1494:735.137 - 0.029ms returns 0x00000000
T6838 1494:735.151 JLINK_ReadReg(FPS20)
T6838 1494:735.164 - 0.013ms returns 0x00000000
T6838 1494:735.178 JLINK_ReadReg(FPS21)
T6838 1494:735.192 - 0.013ms returns 0x00000000
T6838 1494:735.206 JLINK_ReadReg(FPS22)
T6838 1494:735.221 - 0.014ms returns 0x00000000
T6838 1494:735.235 JLINK_ReadReg(FPS23)
T6838 1494:735.254 - 0.018ms returns 0x00000000
T6838 1494:735.268 JLINK_ReadReg(FPS24)
T6838 1494:735.283 - 0.014ms returns 0x00000000
T6838 1494:735.298 JLINK_ReadReg(FPS25)
T6838 1494:735.312 - 0.014ms returns 0x00000000
T6838 1494:735.328 JLINK_ReadReg(FPS26)
T6838 1494:735.342 - 0.013ms returns 0x00000000
T6838 1494:735.356 JLINK_ReadReg(FPS27)
T6838 1494:735.370 - 0.013ms returns 0x00000000
T6838 1494:735.385 JLINK_ReadReg(FPS28)
T6838 1494:735.400 - 0.015ms returns 0x00000000
T6838 1494:735.414 JLINK_ReadReg(FPS29)
T6838 1494:735.428 - 0.013ms returns 0x00000000
T6838 1494:735.442 JLINK_ReadReg(FPS30)
T6838 1494:735.455 - 0.013ms returns 0x00000000
T6838 1494:735.469 JLINK_ReadReg(FPS31)
T6838 1494:735.482 - 0.013ms returns 0x00000000
T2220 1494:741.405 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T2220 1494:741.536   CPU_ReadMem(64 bytes @ 0x00000000)
T2220 1494:743.877    -- Updating C cache (64 bytes @ 0x00000000)
T2220 1494:743.962    -- Read from C cache (4 bytes @ 0x00000000)
T2220 1494:744.034   Data:  00 04 00 20
T2220 1494:744.105 - 2.701ms returns 4 (0x4)
T2220 1494:752.653 JLINK_HasError()
T2220 1494:752.715 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T2220 1494:752.753   CPU_ReadMem(4 bytes @ 0xE0001004)
T2220 1494:753.808   Data:  1D 45 A4 02
T2220 1494:753.871   Debug reg: DWT_CYCCNT
T2220 1494:753.903 - 1.187ms returns 1 (0x1)
T2220 1494:758.489 JLINK_ReadMemEx(0x00027ABC, 0x3C Bytes, Flags = 0x02000000)
T2220 1494:758.574   CPU_ReadMem(128 bytes @ 0x00027A80)
T2220 1494:762.047    -- Updating C cache (128 bytes @ 0x00027A80)
T2220 1494:762.177    -- Read from C cache (60 bytes @ 0x00027ABC)
T2220 1494:762.253   Data:  00 BE 02 20 00 EB 04 40 0E A1 04 F0 5F FC BF F3 ...
T2220 1494:762.325 - 3.835ms returns 60 (0x3C)
T2220 1720:715.216 JLINK_HasError()
T2220 1720:717.786 JLINK_Close()
T2220 1720:718.599   CPU_WriteMem(4 bytes @ 0xE0002008)
T2220 1720:718.729   CPU_WriteMem(4 bytes @ 0xE000200C)
T2220 1720:718.821   CPU_WriteMem(4 bytes @ 0xE0002010)
T2220 1720:718.899   CPU_WriteMem(4 bytes @ 0xE0002014)
T2220 1720:722.455   CPU_ReadMem(4 bytes @ 0xE0001000)
T2220 1720:741.985 - 24.196ms
T2220 1720:742.127   
T2220 1720:742.189   Closed
