.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.c
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\string.h
.\_build\nrf_atomic.o: ..\config\sdk_config.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\nrf_atomic.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\nrf_atomic.o: ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic_internal.h
