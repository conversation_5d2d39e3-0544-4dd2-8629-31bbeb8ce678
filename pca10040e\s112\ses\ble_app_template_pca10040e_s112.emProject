<!DOCTYPE CrossStudio_Project_File>
<solution Name="ble_app_template_pca10040e_s112" target="8" version="2">
  <project Name="ble_app_template_pca10040e_s112">
    <configuration
      Name="Common"
      arm_architecture="v7EM"
      arm_core_type="Cortex-M4"
      arm_endian="Little"
      arm_fp_abi="Soft"
      arm_fpu_type="Soft"
      arm_linker_heap_size="2048"
      arm_linker_process_stack_size="0"
      arm_linker_stack_size="2048"
      arm_linker_treat_warnings_as_errors="No"
      arm_simulator_memory_simulation_parameter="RWX 00000000,00100000,FFFFFFFF;RWX 20000000,00010000,CDCDCDCD"
      arm_target_device_name="nRF52810_xxAA"
      arm_target_interface_type="SWD"
      c_user_include_directories="../../../config;../../../../../../components;../../../../../../components/ble/ble_advertising;../../../../../../components/ble/ble_dtm;../../../../../../components/ble/ble_racp;../../../../../../components/ble/ble_services/ble_ancs_c;../../../../../../components/ble/ble_services/ble_ans_c;../../../../../../components/ble/ble_services/ble_bas;../../../../../../components/ble/ble_services/ble_bas_c;../../../../../../components/ble/ble_services/ble_cscs;../../../../../../components/ble/ble_services/ble_cts_c;../../../../../../components/ble/ble_services/ble_dfu;../../../../../../components/ble/ble_services/ble_dis;../../../../../../components/ble/ble_services/ble_gls;../../../../../../components/ble/ble_services/ble_hids;../../../../../../components/ble/ble_services/ble_hrs;../../../../../../components/ble/ble_services/ble_hrs_c;../../../../../../components/ble/ble_services/ble_hts;../../../../../../components/ble/ble_services/ble_ias;../../../../../../components/ble/ble_services/ble_ias_c;../../../../../../components/ble/ble_services/ble_lbs;../../../../../../components/ble/ble_services/ble_lbs_c;../../../../../../components/ble/ble_services/ble_lls;../../../../../../components/ble/ble_services/ble_nus;../../../../../../components/ble/ble_services/ble_nus_c;../../../../../../components/ble/ble_services/ble_rscs;../../../../../../components/ble/ble_services/ble_rscs_c;../../../../../../components/ble/ble_services/ble_tps;../../../../../../components/ble/common;../../../../../../components/ble/nrf_ble_gatt;../../../../../../components/ble/nrf_ble_qwr;../../../../../../components/ble/peer_manager;../../../../../../components/boards;../../../../../../components/libraries/atomic;../../../../../../components/libraries/atomic_fifo;../../../../../../components/libraries/atomic_flags;../../../../../../components/libraries/balloc;../../../../../../components/libraries/bootloader/ble_dfu;../../../../../../components/libraries/bsp;../../../../../../components/libraries/button;../../../../../../components/libraries/cli;../../../../../../components/libraries/crc16;../../../../../../components/libraries/crc32;../../../../../../components/libraries/crypto;../../../../../../components/libraries/csense;../../../../../../components/libraries/csense_drv;../../../../../../components/libraries/delay;../../../../../../components/libraries/ecc;../../../../../../components/libraries/experimental_section_vars;../../../../../../components/libraries/experimental_task_manager;../../../../../../components/libraries/fds;../../../../../../components/libraries/fstorage;../../../../../../components/libraries/gfx;../../../../../../components/libraries/gpiote;../../../../../../components/libraries/hardfault;../../../../../../components/libraries/hci;../../../../../../components/libraries/led_softblink;../../../../../../components/libraries/log;../../../../../../components/libraries/log/src;../../../../../../components/libraries/low_power_pwm;../../../../../../components/libraries/mem_manager;../../../../../../components/libraries/memobj;../../../../../../components/libraries/mpu;../../../../../../components/libraries/mutex;../../../../../../components/libraries/pwm;../../../../../../components/libraries/pwr_mgmt;../../../../../../components/libraries/queue;../../../../../../components/libraries/ringbuf;../../../../../../components/libraries/scheduler;../../../../../../components/libraries/sdcard;../../../../../../components/libraries/sensorsim;../../../../../../components/libraries/slip;../../../../../../components/libraries/sortlist;../../../../../../components/libraries/spi_mngr;../../../../../../components/libraries/stack_guard;../../../../../../components/libraries/strerror;../../../../../../components/libraries/svc;../../../../../../components/libraries/timer;../../../../../../components/libraries/twi_mngr;../../../../../../components/libraries/twi_sensor;../../../../../../components/libraries/usbd;../../../../../../components/libraries/usbd/class/audio;../../../../../../components/libraries/usbd/class/cdc;../../../../../../components/libraries/usbd/class/cdc/acm;../../../../../../components/libraries/usbd/class/hid;../../../../../../components/libraries/usbd/class/hid/generic;../../../../../../components/libraries/usbd/class/hid/kbd;../../../../../../components/libraries/usbd/class/hid/mouse;../../../../../../components/libraries/usbd/class/msc;../../../../../../components/libraries/util;../../../../../../components/softdevice/common;../../../../../../components/softdevice/s112/headers;../../../../../../components/softdevice/s112/headers/nrf52;../../../../../../components/toolchain/cmsis/include;../../../../../../external/fprintf;../../../../../../external/segger_rtt;../../../../../../external/utf_converter;../../../../../../integration/nrfx;../../../../../../integration/nrfx/legacy;../../../../../../modules/nrfx;../../../../../../modules/nrfx/drivers/include;../../../../../../modules/nrfx/hal;../../../../../../modules/nrfx/mdk;../config;"
      c_preprocessor_definitions="APP_TIMER_V2;APP_TIMER_V2_RTC1_ENABLED;BOARD_PCA10040;CONFIG_GPIO_AS_PINRESET;DEVELOP_IN_NRF52832;FLOAT_ABI_SOFT;INITIALIZE_USER_SECTIONS;NO_VTOR_CONFIG;NRF52810_XXAA;NRF52_PAN_74;NRFX_COREDEP_DELAY_US_LOOP_CYCLES=3;NRF_SD_BLE_API_VERSION=7;S112;SOFTDEVICE_PRESENT;"
      debug_target_connection="J-Link"
      gcc_entry_point="Reset_Handler"
      macros="CMSIS_CONFIG_TOOL=../../../../../../external_tools/cmsisconfig/CMSIS_Configuration_Wizard.jar"
      debug_register_definition_file="../../../../../../modules/nrfx/mdk/nrf52810.svd"
      debug_additional_load_file="../../../../../../components/softdevice/s112/hex/s112_nrf52_7.2.0_softdevice.hex"
      debug_start_from_entry_point_symbol="No"
      gcc_debugging_level="Level 3"      linker_output_format="hex"
      linker_printf_width_precision_supported="Yes"
      linker_printf_fmt_level="long"
      linker_scanf_fmt_level="long"
      linker_section_placement_file="flash_placement.xml"
      linker_section_placement_macros="FLASH_PH_START=0x0;FLASH_PH_SIZE=0x30000;RAM_PH_START=0x20000000;RAM_PH_SIZE=0x6000;FLASH_START=0x19000;FLASH_SIZE=0x17000;RAM_START=0x20001a40;RAM_SIZE=0x45c0"
      
      linker_section_placements_segments="FLASH1 RX 0x0 0x30000;RAM1 RWX 0x20000000 0x6000"
      project_directory=""
      project_type="Executable" />
      <folder Name="Segger Startup Files">
        <file file_name="$(StudioDir)/source/thumb_crt0.s" />
      </folder>
    <folder Name="nRF_Log">
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_rtt.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_serial.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_backend_uart.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_default_backends.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_frontend.c" />
      <file file_name="../../../../../../components/libraries/log/src/nrf_log_str_formatter.c" />
    </folder>
    <folder Name="nRF_Libraries">
      <file file_name="../../../../../../components/libraries/button/app_button.c" />
      <file file_name="../../../../../../components/libraries/util/app_error.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_handler_gcc.c" />
      <file file_name="../../../../../../components/libraries/util/app_error_weak.c" />
      <file file_name="../../../../../../components/libraries/scheduler/app_scheduler.c" />
      <file file_name="../../../../../../components/libraries/timer/app_timer2.c" />
      <file file_name="../../../../../../components/libraries/util/app_util_platform.c" />
      <file file_name="../../../../../../components/libraries/crc16/crc16.c" />
      <file file_name="../../../../../../components/libraries/timer/drv_rtc.c" />
      <file file_name="../../../../../../components/libraries/fds/fds.c" />
      <file file_name="../../../../../../components/libraries/hardfault/hardfault_implementation.c" />
      <file file_name="../../../../../../components/libraries/util/nrf_assert.c" />
      <file file_name="../../../../../../components/libraries/atomic_fifo/nrf_atfifo.c" />
      <file file_name="../../../../../../components/libraries/atomic_flags/nrf_atflags.c" />
      <file file_name="../../../../../../components/libraries/atomic/nrf_atomic.c" />
      <file file_name="../../../../../../components/libraries/balloc/nrf_balloc.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf.c" />
      <file file_name="../../../../../../external/fprintf/nrf_fprintf_format.c" />
      <file file_name="../../../../../../components/libraries/fstorage/nrf_fstorage.c" />
      <file file_name="../../../../../../components/libraries/fstorage/nrf_fstorage_sd.c" />
      <file file_name="../../../../../../components/libraries/memobj/nrf_memobj.c" />
      <file file_name="../../../../../../components/libraries/pwr_mgmt/nrf_pwr_mgmt.c" />
      <file file_name="../../../../../../components/libraries/ringbuf/nrf_ringbuf.c" />
      <file file_name="../../../../../../components/libraries/experimental_section_vars/nrf_section_iter.c" />
      <file file_name="../../../../../../components/libraries/sortlist/nrf_sortlist.c" />
      <file file_name="../../../../../../components/libraries/strerror/nrf_strerror.c" />
      <file file_name="../../../../../../components/libraries/sensorsim/sensorsim.c" />
    </folder>
    <folder Name="None">
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf52810.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/ses_startup_nrf_common.s" />
      <file file_name="../../../../../../modules/nrfx/mdk/system_nrf52810.c" />
    </folder>
    <folder Name="Board Definition">
      <file file_name="../../../../../../components/boards/boards.c" />
    </folder>
    <folder Name="nRF_Drivers">
      <file file_name="../../../../../../integration/nrfx/legacy/nrf_drv_clock.c" />
      <file file_name="../../../../../../integration/nrfx/legacy/nrf_drv_uart.c" />
      <file file_name="../../../../../../modules/nrfx/soc/nrfx_atomic.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_clock.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_gpiote.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/prs/nrfx_prs.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_uart.c" />
      <file file_name="../../../../../../modules/nrfx/drivers/src/nrfx_uarte.c" />
    </folder>
    <folder Name="Board Support">
      <file file_name="../../../../../../components/libraries/bsp/bsp.c" />
      <file file_name="../../../../../../components/libraries/bsp/bsp_btn_ble.c" />
    </folder>
    <folder Name="Application">
      <file file_name="../../../main.c" />
      <file file_name="../config/sdk_config.h" />
    </folder>
    <folder Name="nRF_Segger_RTT">
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_Syscalls_SES.c" />
      <file file_name="../../../../../../external/segger_rtt/SEGGER_RTT_printf.c" />
    </folder>
    <folder Name="nRF_BLE">
      <file file_name="../../../../../../components/ble/peer_manager/auth_status_tracker.c" />
      <file file_name="../../../../../../components/ble/common/ble_advdata.c" />
      <file file_name="../../../../../../components/ble/ble_advertising/ble_advertising.c" />
      <file file_name="../../../../../../components/ble/common/ble_conn_params.c" />
      <file file_name="../../../../../../components/ble/common/ble_conn_state.c" />
      <file file_name="../../../../../../components/ble/common/ble_srv_common.c" />
      <file file_name="../../../../../../components/ble/peer_manager/gatt_cache_manager.c" />
      <file file_name="../../../../../../components/ble/peer_manager/gatts_cache_manager.c" />
      <file file_name="../../../../../../components/ble/peer_manager/id_manager.c" />
      <file file_name="../../../../../../components/ble/nrf_ble_gatt/nrf_ble_gatt.c" />
      <file file_name="../../../../../../components/ble/nrf_ble_qwr/nrf_ble_qwr.c" />
      <file file_name="../../../../../../components/ble/peer_manager/peer_data_storage.c" />
      <file file_name="../../../../../../components/ble/peer_manager/peer_database.c" />
      <file file_name="../../../../../../components/ble/peer_manager/peer_id.c" />
      <file file_name="../../../../../../components/ble/peer_manager/peer_manager.c" />
      <file file_name="../../../../../../components/ble/peer_manager/peer_manager_handler.c" />
      <file file_name="../../../../../../components/ble/peer_manager/pm_buffer.c" />
      <file file_name="../../../../../../components/ble/peer_manager/security_dispatcher.c" />
      <file file_name="../../../../../../components/ble/peer_manager/security_manager.c" />
    </folder>
    <folder Name="UTF8/UTF16 converter">
      <file file_name="../../../../../../external/utf_converter/utf.c" />
    </folder>
    <folder Name="nRF_SoftDevice">
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh.c" />
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh_ble.c" />
      <file file_name="../../../../../../components/softdevice/common/nrf_sdh_soc.c" />
    </folder>
  </project>
  <configuration Name="Release"
    c_preprocessor_definitions="NDEBUG"
    link_time_optimization="No"    gcc_optimization_level="Optimize For Size" />

</solution>
