#include <stdbool.h>
#include <stdint.h>
#include <string.h>

#include "nordic_common.h"
#include "nrf.h"
#include "app_error.h"
#include "app_timer.h"
#include "fds.h"
#include "sensorsim.h"
#include "nrf_pwr_mgmt.h"

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

//
#include "ble_nus.h"
#include "app_uart.h"
#include "nrf_uart.h"



//my include
#include "spi.h"
#include "ST7789.h"
#include "gui.h"
#include "app_scheduler.h"
#include "nrf_drv_clock.h"
#include "lv_timer.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ble.h"
#include "nrf_sdh_soc.h"

#include "gui_guider.h"
#include "events_init.h"
#include "main.h"
#include "key.h"
#include "ble_conn.h"


///**@brief SoftDevice閿熸枻鎷烽敓鐨嗗洖纰夋嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷?
// *
// * @details 閿熸枻鎷稴oftDevice閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹鏃堕敓鏂ゆ嫹閿熸枻鎷蜂箞鎾曢敓鏂ゆ嫹閿熸枻鎷烽敓?
// *
// * @warning 閿熷壙杈炬嫹閿熸枻鎷烽敓鏂ゆ嫹涓虹ず閿熸枻鎷烽敓鏂ゆ嫹瀹為敓缁炶鎷峰搧閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鐨嗚揪鎷烽敓鏂ゆ嫹閿熸枻鎷峰紡閿熸枻鎷?
// * @warning SoftDevice閿熸枻鎷烽敓鐨嗙尨鎷风郴缁熷彧閿熸枻鎷烽€氶敓鏂ゆ嫹閿熸枻鎷蜂綅閿熻闈╂嫹閿熸枻鎷?
// *
// * @param[in] line_num   閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鐨嗙殑杈炬嫹閿熸枻鎷烽敓鍙彿鈽呮嫹
// * @param[in] file_name  閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鐨嗙鎷烽敓渚ョ》鎷烽敓鏂ゆ嫹閿熸枻鎷?
// */
void assert_nrf_callback(uint16_t line_num, const uint8_t * p_file_name)
{
    app_error_handler(DEAD_BEEF, line_num, p_file_name);
}



///**@brief 閿熸枻鎷锋椂閿熸枻鎷烽敓鏂ゆ嫹濮嬮敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷?
// *
// * @details 閿熸枻鎷峰閿熸枻鎷烽敓鏂ゆ嫹鏃堕敓鏂ゆ嫹妯￠敓浠嬶紝閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷峰簲閿熺煫璁规嫹鏃堕敓鏂ゆ嫹閿熸枻鎷?
// */
static void timers_init(void)
{
    // Initialize timer module.
    ret_code_t err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);

    // Create timers.

    /* YOUR_JOB: 閿熸枻鎷烽敓鏂ゆ嫹搴旈敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹浜╅敓缁炴唻鎷烽敓鏂ゆ嫹閿?
                 閿熸枻鎷烽敓鏂ゆ嫹閿熻杈炬嫹閿熸枻鎷烽敓鏂ゆ嫹鏃堕敓鏂ゆ嫹閿熸枻鎷风ず閿熸枻鎷烽敓鏂ゆ嫹
                 姣忛敓鏂ゆ嫹閿熸枻鎷蜂竴閿熸枻鎷烽敓鏂ゆ嫹鏃堕敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹瑕侀敓鏂ゆ嫹閿熸枻鎷稟PP_TIMER_MAX_TIMERS閿熸枻鎷峰€奸敓鏂ゆ嫹涓€閿熸枻鎷?
       ret_code_t err_code;
       err_code = app_timer_create(&m_app_timer_id, APP_TIMER_MODE_REPEATED, timer_timeout_handler);
       APP_ERROR_CHECK(err_code); */
}


/**@brief Function for starting application timers.
 */
static void application_timers_start(void)
{
    /* YOUR_JOB: Start your timers. Below is an example of how to start a timer.
       ret_code_t err_code;
       err_code = app_timer_start(m_app_timer_id, TIMER_INTERVAL, NULL);
       APP_ERROR_CHECK(err_code); */

}



/**@brief Function for initializing the nrf log module.
 */
static void log_init(void)
{
    ret_code_t err_code = NRF_LOG_INIT(NULL);
    APP_ERROR_CHECK(err_code);

//    NRF_LOG_DEFAULT_BACKENDS_INIT();
}


///**@brief 閿熸枻鎷锋簮閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷峰閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹
// */
static void power_management_init(void)
{
    ret_code_t err_code;
    err_code = nrf_pwr_mgmt_init();
    APP_ERROR_CHECK(err_code);
}


/**@brief 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹鐘舵€侀敓鏂ゆ嫹閿熸枻鎷峰惊閿熸枻鎷烽敓鏂ゆ嫹閿熶茎鐚存嫹閿熸枻鎷烽敓鏂ゆ嫹
 *
 * @details 閿熸枻鎷烽敓鐭紮鎷峰啓閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熻鎾呮嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓琛楁唻鎷烽敓鏂ゆ嫹閿熸彮浼欐嫹閿熸枻鎷峰綍閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓?
 */
static void idle_state_handle(void)
{
    if (NRF_LOG_PROCESS() == false)
    {
        nrf_pwr_mgmt_run();
    }
}


static uint16_t   m_conn_handle          = BLE_CONN_HANDLE_INVALID;  /**< Handle of the current connection. */

/**@brief 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹 Nordic UART 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹鑾告毊閿熸枻鎷烽敓鏂ゆ嫹閿?
 *
 * @details 閿熷壙鐚存嫹閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹 Nordic UART BLE 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷风洀閿熸枻鎷烽敓鏂ゆ嫹鑽╅敓?
 *          閿熸枻鎷烽敓鏂ゆ嫹閿熸垝鍙戦敓閰电鎷?UART 妯￠敓浠嬨€?
 *
 * @param[in] p_evt       Nordic UART 閿熸枻鎷烽敓鏂ゆ嫹閿熼摪纭锋嫹閿熸枻鎷?
 */
/**@snippet [閿熸枻鎷烽敓鏂ゆ嫹閫氶敓鏂ゆ嫹 BLE 閿熸枻鎷烽敓绉哥鎷烽敓鏂ゆ嫹閿熸枻鎷穄 */
static void nus_data_handler(ble_nus_evt_t * p_evt)
{

    if (p_evt->type == BLE_NUS_EVT_RX_DATA)
    {
        uint32_t err_code;

        NRF_LOG_DEBUG("Received data from BLE NUS. Writing data on UART.");
        NRF_LOG_HEXDUMP_DEBUG(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);

        for (uint32_t i = 0; i < p_evt->params.rx_data.length; i++)
        {
            do
            {
                err_code = app_uart_put(p_evt->params.rx_data.p_data[i]);
                if ((err_code != NRF_SUCCESS) && (err_code != NRF_ERROR_BUSY))
                {
                    NRF_LOG_ERROR("Failed sending NUS message. Error 0x%x.", err_code);
                    APP_ERROR_CHECK(err_code);
                }
            } while (err_code == NRF_ERROR_BUSY);
        }
        if (p_evt->params.rx_data.p_data[p_evt->params.rx_data.length - 1] == '\r')
        {
            while (app_uart_put('\n') == NRF_ERROR_BUSY);
        }
    }

}
/**@snippet [閿熸枻鎷烽敓鏂ゆ嫹閫氶敓鏂ゆ嫹 BLE 閿熸枻鎷烽敓绉哥鎷烽敓鏂ゆ嫹閿熸枻鎷穄 */



extern void uart_event_handle(app_uart_evt_t * p_event);

/**@brief  閿熸枻鎷峰閿熸枻鎷?UART 妯￠敓鏂ゆ嫹鏆敓鏂ゆ嫹閿熸枻鎷烽敓?
 */
/**@snippet [UART 閿熸枻鎷峰閿熸枻鎷穄 */
static void uart_init(void)
{
    uint32_t                     err_code;
    app_uart_comm_params_t const comm_params =
    {
        .rx_pin_no    = 8,
        .tx_pin_no    = 6,
        .rts_pin_no   = UART_PIN_DISCONNECTED,  // 缁備胶鏁TS
        .cts_pin_no   = UART_PIN_DISCONNECTED,  // 缁備胶鏁TS
        .flow_control = APP_UART_FLOW_CONTROL_DISABLED,
        .use_parity   = false,
#if defined (UART_PRESENT)
        .baud_rate    = NRF_UART_BAUDRATE_115200
#else
        .baud_rate    = NRF_UARTE_BAUDRATE_115200
#endif
    };

    // 濞ｈ濮炵拫鍐槸娣団剝浼?- 濞夈劍鍓伴敍姘崇箹閺冪rintf閸欘垵鍏樻潻妯圭瑝閼崇晫鏁?
    APP_UART_FIFO_INIT(&comm_params,
                       256,   // 閸戝繐鐨琑X缂傛挸鍟块崠?
                       256,   // 閸戝繐鐨琓X缂傛挸鍟块崠?
                       uart_event_handle,
                       6, 
                       err_code);

    APP_ERROR_CHECK(err_code);
}
/**@snippet [UART 閿熸枻鎷峰閿熸枻鎷穄 */


//ui global variable
lv_ui ui;


/**@brief Application main function.
 */
int main(void)
{
    bool erase_bonds;
    ret_code_t ret;

    
//    // 閿熸枻鎷峰閿熸枻鎷烽敓鏂ゆ嫹鏃堕敓鏂ゆ嫹妯￠敓鏂ゆ嫹
//    timers_init();
//    // 閿熸枻鎷峰閿熸枻鎷烽敓鏂ゆ嫹婧愰敓鏂ゆ嫹閿熸枻鎷锋ā閿熸枻鎷?
//    power_management_init();

//    

//    // 閿熸枻鎷烽敓鏂ゆ嫹鎵ч敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹
//    log("Template example started.");
//    // 閿熸枻鎷烽敓鏂ゆ嫹搴旈敓鐭眰瀹氭椂閿熸枻鎷?
//    application_timers_start();

		uart_init();


		// 閿熸枻鎷峰閿熸枻鎷烽敓鏂ゆ嫹蹇楁ā閿熶粙锛堥敓鏂ゆ嫹閿熻妭纰夋嫹閿熸枻鎷烽敓鏂ゆ嫹鎭敓鏂ゆ嫹閿熸枻鎷烽敓?
    log_init();


		comm_ble_init();
 
    // Initialize the scheduler
    APP_SCHED_INIT(APP_TIMER_SCHED_EVENT_DATA_SIZE, 20);
//    // 閿熸枻鎷风ず閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹濮嬮敓鏂ゆ嫹
//    st7789_init();
//    // 鍥鹃敓杞挎枻鎷烽敓鏂ゆ嫹閿熺粸纭锋嫹閿?
//    gui_init();
//    // 閿熸枻鎷烽敓鏂ゆ嫹UI閿熸枻鎷烽敓鏂ゆ嫹
//    setup_ui(&ui);
//    // 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷峰閿熸枻鎷?
//    key_init();

    // 閿熸枻鎷峰惊閿熸枻鎷?
    for (;;)
    {
//        // 鎵ч敓鍙鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鍙鎷烽敓閾扮》鎷?
//        app_sched_execute();
//        // LVGL鍥鹃敓杞垮簱瀹氭椂閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷峰埛閿熼摪鏂ゆ嫹閿熻姤锛?
//        lv_timer_handler();
        // 閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷烽敓鏂ゆ嫹鐘舵€侀敓鏂ゆ嫹閿熼叺鐧告嫹閿熸枻鎷?閿熸枻鎷峰織閿熸枻鎷烽敓鏂ゆ嫹閿熸枻鎷?
        idle_state_handle();
    }
}


/**
 * @}
 */
