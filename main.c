/**
 * @file main.c
 * @brief Main application file for BLE peripheral template
 * @details This file contains the main application logic for a BLE peripheral device
 *          with UART communication and display functionality.
 */

#include <stdbool.h>
#include <stdint.h>
#include <string.h>

// Nordic SDK includes
#include "nordic_common.h"
#include "nrf.h"
#include "app_error.h"
#include "app_timer.h"
#include "fds.h"
#include "sensorsim.h"
#include "nrf_pwr_mgmt.h"

// Logging includes
#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

// BLE and UART includes
#include "ble_nus.h"
#include "app_uart.h"
#include "nrf_uart.h"

// Application specific includes
#include "spi.h"
#include "ST7789.h"
#include "gui.h"
#include "app_scheduler.h"
#include "nrf_drv_clock.h"
#include "lv_timer.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ble.h"
#include "nrf_sdh_soc.h"
#include "gui_guider.h"
#include "events_init.h"
#include "main.h"
#include "key.h"
#include "ble_conn.h"



/**
 * @brief Function for the Timer initialization
 * @details Initializes the timer module. This creates and starts application timers
 */
static void timers_init(void)
{
    ret_code_t err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);

    // Create application timers here if needed
    // Example:
    // err_code = app_timer_create(&m_app_timer_id, APP_TIMER_MODE_REPEATED, timer_timeout_handler);
    // APP_ERROR_CHECK(err_code);
}

/**
 * @brief Function for starting application timers
 */
static void application_timers_start(void)
{
    // Start application timers here if needed
    // Example:
    // ret_code_t err_code = app_timer_start(m_app_timer_id, TIMER_INTERVAL, NULL);
    // APP_ERROR_CHECK(err_code);
}

/**
 * @brief Function for initializing the nrf log module
 */
static void log_init(void)
{
    ret_code_t err_code = NRF_LOG_INIT(NULL);
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Function for initializing power management
 */
static void power_management_init(void)
{
    ret_code_t err_code = nrf_pwr_mgmt_init();
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Function for handling the idle state (main loop)
 * @details If there is no pending log operation, then sleep until next event occurs
 */
static void idle_state_handle(void)
{
    if (NRF_LOG_PROCESS() == false)
    {
        nrf_pwr_mgmt_run();
    }
}

// Handle of the current BLE connection
static uint16_t m_conn_handle = BLE_CONN_HANDLE_INVALID;

/**
 * @brief Function for handling the data from the Nordic UART Service
 * @details This function will process the data received from the Nordic UART BLE Service
 *          and send it to the UART module
 * @param[in] p_evt Nordic UART Service event
 */
static void nus_data_handler(ble_nus_evt_t * p_evt)
{
    if (p_evt->type == BLE_NUS_EVT_RX_DATA)
    {
        uint32_t err_code;

        NRF_LOG_DEBUG("Received data from BLE NUS. Writing data on UART.");
        NRF_LOG_HEXDUMP_DEBUG(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);

        for (uint32_t i = 0; i < p_evt->params.rx_data.length; i++)
        {
            do
            {
                err_code = app_uart_put(p_evt->params.rx_data.p_data[i]);
                if ((err_code != NRF_SUCCESS) && (err_code != NRF_ERROR_BUSY))
                {
                    NRF_LOG_ERROR("Failed sending NUS message. Error 0x%x.", err_code);
                    APP_ERROR_CHECK(err_code);
                }
            } while (err_code == NRF_ERROR_BUSY);
        }
        
        if (p_evt->params.rx_data.p_data[p_evt->params.rx_data.length - 1] == '\r')
        {
            while (app_uart_put('\n') == NRF_ERROR_BUSY);
        }
    }
}

// External UART event handler declaration
extern void uart_event_handle(app_uart_evt_t * p_event);

/**
 * @brief Function for initializing the UART module
 */ //gd
static void uart_init(void)
{
    uint32_t err_code;
    app_uart_comm_params_t const comm_params =
    {
        .rx_pin_no    = 8,
        .tx_pin_no    = 6,
        .rts_pin_no   = 5,  // RTS pin disabled
        .cts_pin_no   = 7,  // CTS pin disabled
        .flow_control = APP_UART_FLOW_CONTROL_DISABLED,
        .use_parity   = false,
#if defined (UART_PRESENT)
        .baud_rate    = NRF_UART_BAUDRATE_115200
#else
        .baud_rate    = NRF_UARTE_BAUDRATE_115200
#endif
    };

    // Initialize UART with FIFO buffers for printf functionality
    APP_UART_FIFO_INIT(&comm_params,
                       256,   // RX buffer size
                       256,   // TX buffer size
                       uart_event_handle,
                       APP_IRQ_PRIORITY_LOWEST, 
                       err_code);

    APP_ERROR_CHECK(err_code);
}


// UI global variable
lv_ui ui;

/**
 * @brief Application main function
 */
int main(void)
{
    bool erase_bonds;
    ret_code_t ret;

    // Initialize UART for debugging
    uart_init();

    // Initialize logging module (for debugging and information output)
    log_init();

    // Initialize BLE communication
    comm_ble_init();
 
    // Initialize the scheduler
//    APP_SCHED_INIT(APP_TIMER_SCHED_EVENT_DATA_SIZE, 20);

    // Optional initializations (commented out for template)
    // st7789_init();      // Initialize display
    // gui_init();         // Initialize graphics library
    // setup_ui(&ui);      // Setup UI
    // key_init();         // Initialize keys

    // Main loop
    for (;;)
    {
        // Optional scheduled tasks execution
        // app_sched_execute();
        
        // Optional LVGL graphics library timer handler for screen refresh
        // lv_timer_handler();
        
        // Handle idle state (power management and logging)
        idle_state_handle();
    }
}
