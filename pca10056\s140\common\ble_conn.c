/**
 * @file ble_conn.c
 * @brief BLE connection management module
 * @details This file contains all BLE related functionality including stack initialization,
 *          GAP parameters, GATT services, advertising, and connection management.
 */

#include "ble_conn.h"

// BLE stack includes
#include "ble_hci.h"
#include "ble_advdata.h"
#include "ble_advertising.h"
#include "ble_conn_params.h"
#include "ble_nus.h"
#include "ble_dis.h"
#include "ble_bas.h"
#include "ble_dfu.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "nrf_ble_gatt.h"
#include "nrf_ble_qwr.h"
#include "nrf_power.h"
#include "nrf_bootloader_info.h"
#include "nrf_pwr_mgmt.h"
#include "app_uart.h"
#include "nrf_uart.h"
#include "app_timer.h"

// Device configuration
#define DIS_DEVICE_NAME                     "YSJ-10"                        // Device name shown in advertising
#define DIS_MANUFACTURER_NAME               "E3A"                           // Manufacturer name for device info service
#define DIS_MODEL_NUM                       "YSJ-10"                        // Model number
#define HW_VER                              "3.0"                           // Hardware version
#define FW_VER                              "v3.3.4"                        // Firmware version

// Advertising configuration
#define APP_ADV_INTERVAL                    64                            //300gd Advertising interval (units of 0.625 ms, 187.5 ms)
#define APP_ADV_DURATION                    18000                           // Advertising duration (180 seconds, units of 10 ms)

// BLE configuration
#define NUS_SERVICE_UUID_TYPE               BLE_UUID_TYPE_VENDOR_BEGIN      // UUID type for Nordic UART Service
#define APP_BLE_OBSERVER_PRIO               3                               // Application BLE observer priority
#define APP_BLE_CONN_CFG_TAG                1                               // Tag for SoftDevice BLE configuration

// Connection parameters
#define MIN_CONN_INTERVAL                   MSEC_TO_UNITS(20, UNIT_1_25_MS) // Minimum acceptable connection interval (20 ms)
#define MAX_CONN_INTERVAL                   MSEC_TO_UNITS(75, UNIT_1_25_MS) //gd Maximum acceptable connection interval (40 ms)
#define SLAVE_LATENCY                       0                               // Slave latency
#define CONN_SUP_TIMEOUT                    MSEC_TO_UNITS(4000, UNIT_10_MS) // Connection supervisory timeout (4 seconds)

// Connection parameter update delays
#define FIRST_CONN_PARAMS_UPDATE_DELAY      APP_TIMER_TICKS(5000)           // Time from initiating event to first connection parameter update (5 seconds)
#define NEXT_CONN_PARAMS_UPDATE_DELAY       APP_TIMER_TICKS(30000)          // Time between subsequent connection parameter updates (30 seconds)
#define MAX_CONN_PARAMS_UPDATE_COUNT        3                               // Number of attempts before giving up parameter negotiation

#define DEAD_BEEF                           0xDEADBEEF                      // Value used as error code on stack dump

// BLE service instances
BLE_NUS_DEF(m_nus, NRF_SDH_BLE_TOTAL_LINK_COUNT);                          // BLE NUS service instance
NRF_BLE_GATT_DEF(m_gatt);                                                   // GATT module instance
NRF_BLE_QWR_DEF(m_qwr);                                                     // Queued Write module instance
BLE_ADVERTISING_DEF(m_advertising);                                         // Advertising module instance
BLE_BAS_DEF(m_bas);                                                         // Battery service instance

// Global variables
uint16_t m_ble_nus_max_data_len = BLE_GATT_ATT_MTU_DEFAULT - 3;             // Maximum data length for NUS
static uint16_t m_conn_handle = BLE_CONN_HANDLE_INVALID;                    // Current connection handle
static uint32_t m_is_ble_advertising = 0;                                   // Advertising status flag
char ble_name_buf[50];                                                      // Buffer for device name

// Service UUIDs for advertising
static ble_uuid_t m_adv_uuids[] =
{
    {BLE_UUID_NUS_SERVICE, NUS_SERVICE_UUID_TYPE}
};

/**
 * @brief SoftDevice assertion callback
 * @details This function will be called in case of an assertion in the SoftDevice
 * @warning This handler is an example only and does not fit a final product
 * @warning On assert from the SoftDevice, the system can only recover on reset
 * @param[in] line_num   Line number of the failing ASSERT call
 * @param[in] file_name  File name of the failing ASSERT call
 */
void assert_nrf_callback(uint16_t line_num, const uint8_t * p_file_name)
{
    app_error_handler(DEAD_BEEF, line_num, p_file_name);
}

/**
 * @brief BLE event handler
 * @details Handles BLE stack events
 * @param[in] p_ble_evt BLE stack event
 * @param[in] p_context Unused parameter
 */
static void ble_evt_handler(ble_evt_t const * p_ble_evt, void * p_context)
{
    uint32_t err_code;

    switch (p_ble_evt->header.evt_id)
    {
        case BLE_GAP_EVT_CONNECTED:
            NRF_LOG_INFO("Connected");
            m_conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
            err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GAP_EVT_DISCONNECTED:
            NRF_LOG_INFO("Disconnected");
            m_conn_handle = BLE_CONN_HANDLE_INVALID;
            break;

        case BLE_GAP_EVT_PHY_UPDATE_REQUEST:
        {
            NRF_LOG_DEBUG("PHY update request.");
            ble_gap_phys_t const phys =
            {
                .rx_phys = BLE_GAP_PHY_AUTO,
                .tx_phys = BLE_GAP_PHY_AUTO,
            };
            err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
            APP_ERROR_CHECK(err_code);
        } break;

        case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
            // Pairing not supported
            err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTS_EVT_SYS_ATTR_MISSING:
            // No system attributes have been stored
            err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTC_EVT_TIMEOUT:
            // Disconnect on GATT Client timeout event
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTS_EVT_TIMEOUT:
            // Disconnect on GATT Server timeout event
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            break;

        default:
            // No implementation needed
            break;
    }
}

/**
 * @brief BLE stack initialization
 * @details Initializes the SoftDevice and the BLE event interrupt
 */
static void ble_stack_init(void)
{
    ret_code_t err_code;

    err_code = nrf_sdh_enable_request();
    APP_ERROR_CHECK(err_code);

    // Configure the BLE stack using the default settings
    // Fetch the start address of the application RAM
    uint32_t ram_start = 0;
    err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
    APP_ERROR_CHECK(err_code);

    // Enable BLE stack
    err_code = nrf_sdh_ble_enable(&ram_start);
    APP_ERROR_CHECK(err_code);

    // Register a handler for BLE events
    NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

/**
 * @brief GAP initialization
 * @details Sets up device name and GAP connection parameters
 */
static void gap_params_init(void)
{
    ret_code_t err_code;
    ble_gap_conn_params_t gap_conn_params;
    ble_gap_conn_sec_mode_t sec_mode;
    
    BLE_GAP_CONN_SEC_MODE_SET_OPEN(&sec_mode);
    
//gd    // Get device address for unique naming
//    ble_gap_addr_t addr;
//    err_code = sd_ble_gap_addr_get(&addr);
//    APP_ERROR_CHECK(err_code);
//    
//    // Create unique device name
//    memset(ble_name_buf, 0, sizeof(ble_name_buf));
//    snprintf(ble_name_buf, sizeof(ble_name_buf), "%s_%0.5d", DIS_DEVICE_NAME, addr.addr[0] * addr.addr[1]);

    err_code = sd_ble_gap_device_name_set(&sec_mode,
                                          (const uint8_t *)ble_name_buf,
                                          strlen(ble_name_buf));
    APP_ERROR_CHECK(err_code);

    // Set connection parameters
    memset(&gap_conn_params, 0, sizeof(gap_conn_params));
    gap_conn_params.min_conn_interval = MIN_CONN_INTERVAL;
    gap_conn_params.max_conn_interval = MAX_CONN_INTERVAL;
    gap_conn_params.slave_latency     = SLAVE_LATENCY;
    gap_conn_params.conn_sup_timeout  = CONN_SUP_TIMEOUT;

    err_code = sd_ble_gap_ppcp_set(&gap_conn_params);
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief GATT event handler
 * @details Handles events from the GATT library
 */
static void gatt_evt_handler(nrf_ble_gatt_t * p_gatt, nrf_ble_gatt_evt_t const * p_evt)
{
    if ((m_conn_handle == p_evt->conn_handle) && (p_evt->evt_id == NRF_BLE_GATT_EVT_ATT_MTU_UPDATED))
    {
        m_ble_nus_max_data_len = p_evt->params.att_mtu_effective - OPCODE_LENGTH - HANDLE_LENGTH;
        NRF_LOG_INFO("Data len is set to 0x%X(%d)", m_ble_nus_max_data_len, m_ble_nus_max_data_len);
    }
    NRF_LOG_DEBUG("ATT MTU exchange completed. central 0x%x peripheral 0x%x",
                  p_gatt->att_mtu_desired_central,
                  p_gatt->att_mtu_desired_periph);
}

/**
 * @brief GATT module initialization
 */
void gatt_init(void)
{
    ret_code_t err_code;

    err_code = nrf_ble_gatt_init(&m_gatt, gatt_evt_handler);
    APP_ERROR_CHECK(err_code);

    err_code = nrf_ble_gatt_att_mtu_periph_set(&m_gatt, NRF_SDH_BLE_GATT_MAX_MTU_SIZE);
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Queued Write Module error handler
 * @param[in] nrf_error Error code containing information about what went wrong
 */
static void nrf_qwr_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}

/**
 * @brief Nordic UART Service data handler
 * @details Handles data received from the Nordic UART BLE Service
 * @param[in] p_evt Nordic UART Service event
 */
static void nus_data_handler(ble_nus_evt_t * p_evt)
{
    if (p_evt->type == BLE_NUS_EVT_RX_DATA)
    {
        // Data received - process here if needed
        NRF_LOG_INFO("Received %d bytes via BLE NUS", p_evt->params.rx_data.length);
    }
    else if (p_evt->type == BLE_NUS_EVT_TX_RDY)
    {
        // Ready to send more data
        NRF_LOG_DEBUG("BLE NUS ready to send more data");
    }
    else if (p_evt->type == BLE_NUS_EVT_COMM_STARTED)
    {
        NRF_LOG_INFO("NUS Notification enabled");
    }
    else if (p_evt->type == BLE_NUS_EVT_COMM_STOPPED)
    {
        NRF_LOG_INFO("NUS Notification disabled");
    }
}

/**
 * @brief Check if BLE is connected
 * @return true if connected, false otherwise
 */
bool ble_conn_is_connected(void)
{
    return (m_conn_handle != BLE_CONN_HANDLE_INVALID);
}

/**
 * @brief Send data through Nordic UART Service
 * @param[in] data Pointer to data to send
 * @param[in] len Length of data
 * @return Error code
 */
uint32_t nus_send_byte(uint8_t* data, uint16_t len)
{
    return ble_nus_data_send(&m_nus, data, &len, m_conn_handle);
}

/**
 * @brief Buttonless DFU state observer
 */
static void buttonless_dfu_sdh_state_observer(nrf_sdh_state_evt_t state, void * p_context)
{
    if (state == NRF_SDH_EVT_STATE_DISABLED)
    {
        // Softdevice was disabled before going into reset
        nrf_power_gpregret2_set(BOOTLOADER_DFU_SKIP_CRC);
        nrf_pwr_mgmt_shutdown(NRF_PWR_MGMT_SHUTDOWN_GOTO_SYSOFF);
    }
}

// nrf_sdh state observer
NRF_SDH_STATE_OBSERVER(m_buttonless_dfu_state_obs, 0) =
{
    .handler = buttonless_dfu_sdh_state_observer,
};

/**
 * @brief Get advertising configuration
 */
static void advertising_config_get(ble_adv_modes_config_t * p_config)
{
    memset(p_config, 0, sizeof(ble_adv_modes_config_t));
    p_config->ble_adv_fast_enabled  = true;
    p_config->ble_adv_fast_interval = APP_ADV_INTERVAL;
    p_config->ble_adv_fast_timeout  = APP_ADV_DURATION;
}

/**
 * @brief Disconnect a connection
 */
static void disconnect(uint16_t conn_handle, void * p_context)
{
    UNUSED_PARAMETER(p_context);
    ret_code_t err_code = sd_ble_gap_disconnect(conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
    if (err_code != NRF_SUCCESS)
    {
        NRF_LOG_WARNING("Failed to disconnect connection. Handle: %d Error: %d", conn_handle, err_code);
    }
    else
    {
        NRF_LOG_DEBUG("Disconnected connection handle %d", conn_handle);
    }
}

/**
 * @brief Advertising event handler
 */
static void on_adv_evt(ble_adv_evt_t ble_adv_evt)
{
    switch (ble_adv_evt)
    {
        case BLE_ADV_EVT_FAST:
            m_is_ble_advertising = 1;
            break;
        case BLE_ADV_EVT_IDLE:
            m_is_ble_advertising = 0;
            break;
        default:
            break;
    }
}

/**
 * @brief DFU event handler
 * @param[in] event Event from the Buttonless Secure DFU service
 */
static void ble_dfu_evt_handler(ble_dfu_buttonless_evt_type_t event)
{
    switch (event)
    {
        case BLE_DFU_EVT_BOOTLOADER_ENTER_PREPARE:
        {
            NRF_LOG_INFO("Device is preparing to enter bootloader mode.");

            // Prevent device from advertising on disconnect
            ble_adv_modes_config_t config;
            advertising_config_get(&config);
            config.ble_adv_on_disconnect_disabled = true;
            ble_advertising_modes_config_set(&m_advertising, &config);

            // Disconnect all connected devices
            uint32_t conn_count = ble_conn_state_for_each_connected(disconnect, NULL);
            NRF_LOG_INFO("Disconnected %d links.", conn_count);
            break;
        }

        case BLE_DFU_EVT_BOOTLOADER_ENTER:
            NRF_LOG_INFO("Device will enter bootloader mode.");
            break;

        case BLE_DFU_EVT_BOOTLOADER_ENTER_FAILED:
            NRF_LOG_ERROR("Request to enter bootloader mode failed.");
            break;

        case BLE_DFU_EVT_RESPONSE_SEND_ERROR:
            NRF_LOG_ERROR("Request to send a response to client failed.");
            APP_ERROR_CHECK(false);
            break;

        default:
            NRF_LOG_ERROR("Unknown event from ble_dfu_buttonless.");
            break;
    }
}

/**
 * @brief Advertising initialization
 */
static void advertising_init(void)
{
    uint32_t err_code;
    ble_advertising_init_t init;

    memset(&init, 0, sizeof(init));

    init.advdata.name_type          = BLE_ADVDATA_FULL_NAME;
    init.advdata.include_appearance = false;
    init.advdata.flags              = BLE_GAP_ADV_FLAGS_LE_ONLY_LIMITED_DISC_MODE;

    init.srdata.uuids_complete.uuid_cnt = sizeof(m_adv_uuids) / sizeof(m_adv_uuids[0]);
    init.srdata.uuids_complete.p_uuids  = m_adv_uuids;

//gd    init.config.ble_adv_on_disconnect_disabled = false; // Auto restart advertising on disconnect
    init.config.ble_adv_fast_enabled  = true;
    init.config.ble_adv_fast_interval = APP_ADV_INTERVAL;
    init.config.ble_adv_fast_timeout  = APP_ADV_DURATION;
    init.evt_handler = on_adv_evt;

    err_code = ble_advertising_init(&m_advertising, &init);
    APP_ERROR_CHECK(err_code);

    ble_advertising_conn_cfg_tag_set(&m_advertising, APP_BLE_CONN_CFG_TAG);
}

/**
 * @brief Start advertising
 */
void advertising_start(void)
{
    uint32_t err_code = ble_advertising_start(&m_advertising, BLE_ADV_MODE_FAST);
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Stop advertising
 */
void advertising_stop(void)
{
    if (m_is_ble_advertising)
    {
        (void)sd_ble_gap_adv_stop(m_advertising.adv_handle);
    }
}

/**
 * @brief Initialize services that will be used by the application
 */
static void services_init(void)
{
    uint32_t err_code;
    ble_nus_init_t nus_init;
    nrf_ble_qwr_init_t qwr_init = {0};
//gd    ble_dis_init_t dis_init;
//    ble_bas_init_t bas_init;
//    ble_dfu_buttonless_init_t dfus_init = {0};

    // Initialize Queued Write Module
    qwr_init.error_handler = nrf_qwr_error_handler;
    err_code = nrf_ble_qwr_init(&m_qwr, &qwr_init);
    APP_ERROR_CHECK(err_code);

    // Initialize Nordic UART Service
    memset(&nus_init, 0, sizeof(nus_init));
    nus_init.data_handler = nus_data_handler;
    err_code = ble_nus_init(&m_nus, &nus_init);
    APP_ERROR_CHECK(err_code);

    // Initialize Device Information Service
//    memset(&dis_init, 0, sizeof(dis_init));

    // Create serial number from device address if not available
//    char str1[20];
//    memset(str1, 0, sizeof(str1));
//    ble_gap_addr_t addr;
//    err_code = sd_ble_gap_addr_get(&addr);
//    APP_ERROR_CHECK(err_code);
//    snprintf(str1, sizeof(str1), "%0.5d", addr.addr[0] * addr.addr[1]);

//    char str2[20];
//    snprintf(str2, sizeof(str2), "HW_V%s", HW_VER);

//    char str3[30];
//    snprintf(str3, sizeof(str3), "%s %s %s", FW_VER, __DATE__, __TIME__);

//    ble_srv_ascii_to_utf8(&dis_init.manufact_name_str, DIS_MANUFACTURER_NAME);
//    ble_srv_ascii_to_utf8(&dis_init.model_num_str, DIS_MODEL_NUM);
//    ble_srv_ascii_to_utf8(&dis_init.serial_num_str, str1);
//    ble_srv_ascii_to_utf8(&dis_init.hw_rev_str, str2);
//    ble_srv_ascii_to_utf8(&dis_init.fw_rev_str, str3);
//    dis_init.dis_char_rd_sec = SEC_OPEN;

//    err_code = ble_dis_init(&dis_init);
//    APP_ERROR_CHECK(err_code);

//    // Initialize Battery Service
//    memset(&bas_init, 0, sizeof(bas_init));
//    bas_init.bl_rd_sec        = SEC_OPEN;
//    bas_init.bl_cccd_wr_sec   = SEC_OPEN;
//    bas_init.bl_report_rd_sec = SEC_OPEN;
//    bas_init.evt_handler          = NULL;
//    bas_init.support_notification = true;
//    bas_init.p_report_ref         = NULL;
//    bas_init.initial_batt_level   = 100;

//    err_code = ble_bas_init(&m_bas, &bas_init);
//    APP_ERROR_CHECK(err_code);

//    // Initialize DFU service
//    dfus_init.evt_handler = ble_dfu_evt_handler;
//    err_code = ble_dfu_buttonless_init(&dfus_init);
//    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Update battery level
 * @param[in] battery_level Battery level (0-100)
 */
void ble_battery_level_update(uint32_t battery_level)
{
    APP_ERROR_CHECK_BOOL(battery_level <= 100);

    uint32_t err_code = ble_bas_battery_level_update(&m_bas, battery_level, BLE_CONN_HANDLE_ALL);
    if ((err_code != NRF_SUCCESS) &&
        (err_code != NRF_ERROR_INVALID_STATE) &&
        (err_code != NRF_ERROR_RESOURCES) &&
        (err_code != BLE_ERROR_GATTS_SYS_ATTR_MISSING))
    {
        APP_ERROR_HANDLER(err_code);
    }
}

/**
 * @brief Connection parameters event handler
 * @param[in] p_evt Event received from the Connection Parameters Module
 */
static void on_conn_params_evt(ble_conn_params_evt_t * p_evt)
{
    uint32_t err_code;

    if (p_evt->evt_type == BLE_CONN_PARAMS_EVT_FAILED)
    {
        err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_CONN_INTERVAL_UNACCEPTABLE);
        APP_ERROR_CHECK(err_code);
    }
}

/**
 * @brief Connection parameters error handler
 * @param[in] nrf_error Error code containing information about what went wrong
 */
static void conn_params_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}

/**
 * @brief Connection parameters module initialization
 */
static void conn_params_init(void)
{
    ret_code_t err_code;
    ble_conn_params_init_t cp_init;

    memset(&cp_init, 0, sizeof(cp_init));

    cp_init.p_conn_params                  = NULL;
    cp_init.first_conn_params_update_delay = FIRST_CONN_PARAMS_UPDATE_DELAY;
    cp_init.next_conn_params_update_delay  = NEXT_CONN_PARAMS_UPDATE_DELAY;
    cp_init.max_conn_params_update_count   = MAX_CONN_PARAMS_UPDATE_COUNT;
    cp_init.start_on_notify_cccd_handle    = BLE_GATT_HANDLE_INVALID;
    cp_init.disconnect_on_fail             = false;
    cp_init.evt_handler                    = on_conn_params_evt;
    cp_init.error_handler                  = conn_params_error_handler;

    err_code = ble_conn_params_init(&cp_init);
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief Check if BLE is connected
 * @return true if connected, false otherwise
 */
bool is_ble_connected(void)
{
    return (m_conn_handle != BLE_CONN_HANDLE_INVALID);
}

/**
 * @brief Disconnect BLE connection
 */
void disconnect_BLE(void)
{
    if (m_conn_handle != BLE_CONN_HANDLE_INVALID)
    {
        ret_code_t err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        if (err_code != NRF_SUCCESS)
        {
            NRF_LOG_WARNING("Failed to disconnect connection. Handle: %d Error: %d", m_conn_handle, err_code);
        }
        else
        {
            NRF_LOG_DEBUG("Disconnected connection handle %d", m_conn_handle);
        }
    }
}

/**
 * @brief BLE communication initialization
 * @details Initializes the entire BLE stack and starts advertising
 */
void comm_ble_init(void)
{
    // Initialize BLE protocol stack
    ble_stack_init();

    // Initialize GAP parameters (device name, connection parameters, etc.)
    gap_params_init();

    // Initialize GATT module
    gatt_init();

    // Initialize custom and standard BLE services
    services_init();

    // Initialize advertising functionality
    advertising_init();

    // Initialize connection parameters module
    conn_params_init();

    // Start advertising (can be controlled as needed)
    advertising_start();
}

/**
 * @brief UART event handler for BLE communication
 * @details Handles UART events and forwards data to BLE when appropriate
 * @param[in] p_event UART event
 */
void uart_event_handle(app_uart_evt_t * p_event)
{
    static uint8_t data_array[BLE_NUS_MAX_DATA_LEN];
    static uint8_t index = 0;
    uint32_t err_code;

    switch (p_event->evt_type)
    {
        case APP_UART_DATA_READY:
            UNUSED_VARIABLE(app_uart_get(&data_array[index]));
            index++;

            if ((data_array[index - 1] == '\n') ||
                (data_array[index - 1] == '\r') ||
                (index >= m_ble_nus_max_data_len))
            {
                if (index > 1)
                {
                    NRF_LOG_DEBUG("Ready to send data via BLE NUS");
                    NRF_LOG_HEXDUMP_DEBUG(data_array, index);

                    do
                    {
                        uint16_t length = (uint16_t)index;
                        err_code = ble_nus_data_send(&m_nus, data_array, &length, m_conn_handle);
                        if ((err_code != NRF_ERROR_INVALID_STATE) &&
                            (err_code != NRF_ERROR_RESOURCES) &&
                            (err_code != NRF_ERROR_NOT_FOUND))
                        {
                            APP_ERROR_CHECK(err_code);
                        }
                    } while (err_code == NRF_ERROR_RESOURCES);
                }

                index = 0;
            }
            break;

        case APP_UART_COMMUNICATION_ERROR:
            APP_ERROR_HANDLER(p_event->data.error_communication);
            break;

        case APP_UART_FIFO_ERROR:
            APP_ERROR_HANDLER(p_event->data.error_code);
            break;

        default:
            break;
    }
}
